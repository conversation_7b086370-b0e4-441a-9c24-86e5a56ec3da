.ui-text {
	@apply text-sm leading-5;
}
.ui-text-xs {
	@apply text-xs leading-4;
}
.ui-muted {
	@apply text-gray-600;
}
.ui-chip {
	@apply inline-flex items-center gap-1 rounded px-1.5 py-0.5;
}
.ui-btn-focus {
	@apply outline-offset-2 focus-visible:outline focus-visible:outline-2 focus-visible:outline-primary-600;
}
.ui-table-head {
	@apply bg-gray-50 text-xs font-medium uppercase tracking-wide text-gray-600;
}
.ui-td {
	@apply px-3 py-2 align-middle;
}
.ui-th {
	@apply px-3 py-2 text-left;
}
.ui-row {
	@apply focus-within:bg-gray-50 hover:bg-gray-50;
}
.ui-sticky-head {
	@apply sticky top-0 z-10;
}
.ui-mono {
	@apply font-mono;
}
