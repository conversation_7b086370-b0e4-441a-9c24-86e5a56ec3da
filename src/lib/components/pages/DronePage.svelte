<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { get } from 'svelte/store';
	import { userProfile } from '$lib/stores';
	import {
		Button,
		Badge,
		Table,
		TableBody,
		TableBodyCell,
		TableBodyRow,
		TableHead,
		TableHeadCell,
		Toast,
		Select,
		Alert
	} from 'flowbite-svelte';
	import {
		fetchGliders,
		fetchMaintenancesByGliderId,
		fetchGliderStatusLogs,
		fetchFlightsByGlider,
		fetchIncidents,
		type Glider,
		type GliderMaintenance,
		type GliderStatusLog,
		type FlightData,
		type IncidentData
	} from '$lib/services/gliders.service';
	import { fetchNetworkDevices, type NetworkDevice } from '$lib/services/network.service';
	import { fetchUsers, type UserData } from '$lib/services/users.service';
	import { CheckCircleOutline, ArrowLeftOutline, CogOutline } from 'flowbite-svelte-icons';
	import { formatDate, formatFlightTime } from '$lib/utils/datetime';
	import EditDroneModal from '$lib/components/common/modals/EditDroneModal.svelte';
	import RequestMaintenanceModal from '$lib/components/common/modals/RequestMaintenanceModal.svelte';
	import GliderStatusBadge from '$lib/components/features/gliders/GliderStatusBadge.svelte';
	import SoftwareVersionManager from '$lib/components/features/software/SoftwareVersionManager.svelte';
	import ConfirmSoftwareVersionModal from '$lib/components/common/modals/ConfirmSoftwareVersionModal.svelte';
	import { requestUnscheduledMaintenance } from '$lib/services/maintenance-request.service';
	import {
		getSoftwareVersionsByTypeId,
		updateGliderSoftwareVersion
	} from '$lib/services/gliders.service';
	import type { UnscheduledMaintenanceRequest } from '$lib/types/maintenance-request.types';
	import type { Software } from '$lib/types/software.types';
	import { toast } from 'svelte-sonner';
	import SectionCard from '$lib/components/common/SectionCard.svelte';

	export let droneId: string;

	import '$lib/styles/compact.scss';

	const TEXTS = {
		loading: 'Loading...',
		loadingDroneData: 'Loading drone data...',
		noDeviceFound: 'No device found',
		noIncidents: 'No incidents recorded',
		noMaintenance: 'No maintenance records',
		noFlights: 'No recent flights',
		noScheduledFlights: 'No scheduled flights',
		noStatusChanges: 'No status changes recorded',
		selectUser: 'Select a user',
		connected: 'Connected',
		offline: 'Offline',
		online: 'ONLINE',
		none: 'None',
		empty: '-',
		backToFleet: 'Back to Fleet',
		requestMaintenance: 'Request Maintenance',
		totalFlights: 'Total Flights',
		flightHours: 'Flight Hours',
		droneInformation: 'Drone Information',
		vpnIp: 'VPN IP',
		region: 'Region',
		company: 'Company',
		manufacturingDate: 'Manufacturing Date',
		flightHoursSinceLastMaintenance: 'Flight Hours Since Last Maintenance',
		pixhawkUuid: 'Pixhawk UUID',
		networkDevices: 'Network Devices',
		filters: 'Filters',
		deviceType: 'Device type',
		deviceTypeAll: 'All',
		deviceTypeDrone: 'Drone',
		deviceTypeFts: 'FTS',
		deviceTypeLaptop: 'Laptop',
		deviceTypeSmartphone: 'Smartphone',
		user: 'User',
		search: 'Search',
		name: 'Name',
		role: 'Role',
		type: 'Type',
		ip: 'IP',
		status: 'Status',
		softwareVersions: 'Software Versions',
		component: 'Component',
		current: 'Current',
		desired: 'Desired',
		issues: 'Issues',
		loadingSoftwareVersions: 'Loading software versions...',
		maintenanceHistory: 'Maintenance History',
		dueDate: 'Due Date',
		completed: 'Completed',
		pending: 'Pending',
		staff: 'Staff',
		notes: 'Notes',
		showAll: 'Show all',
		showLess: 'Show less',
		recentIncidents: 'Recent Incidents',
		incidentName: 'Name',
		damage: 'Damage',
		description: 'Description',
		injuries: 'Injuries',
		recentFlights: 'Recent Flights',
		scheduledFlights: 'Scheduled Flights',
		duration: 'Duration',
		distance: 'Distance',
		route: 'Route',
		start: 'Start',
		statusHistory: 'Status History',
		date: 'Date',
		from: 'From',
		to: 'To',
		note: 'Note',
		updateSuccess: 'Drone information updated successfully!'
	};

	const STATUS_COLORS = {
		ready: 'green',
		unavailable: 'yellow',
		grounded: 'red',
		offline: 'dark',
		'maintenance due': 'yellow',
		'post maintenance checks': 'yellow'
	};

	let mounted = true;
	let drone: Glider | null = null;
	let networkDevices: NetworkDevice[] = [];
	let loading = true;
	let error = '';

	$: glider = drone;

	const email = get(userProfile)?.email ?? '';

	let showEditModal = false;
	let showSuccessToast = false;
	let showRequestMaintenanceModal = false;
	let isSubmittingMaintenance = false;

	// Software version management
	let showConfirmVersionModal = false;
	let versionUpdateData: any = null;
	let isUpdatingVersion = false;
	let softwareVersions: { [key: string]: Software[] } = {
		jetson: [],
		autopilot: [],
		ftsPixhawk: [],
		ftsRaspi: []
	};
	let loadingSoftwareVersions = false;

	let users: UserData[] = [];
	let selectedLaptopUserEmail = email;
	let selectedSmartphoneUserEmail = email;

	let maintenanceHistory: GliderMaintenance[] = [];
	let statusLogs: GliderStatusLog[] = [];
	let lastFlights: FlightData[] = [];
	let nextFlights: FlightData[] = [];
	let lastIncidents: IncidentData[] = [];
	let totalFlights = 0;
	let isLoadingMaintenance = false;
	let isLoadingStatusLogs = false;
	let isLoadingFlights = false;
	let isLoadingIncidents = false;

	let deviceTypeFilter: 'all' | 'drone' | 'fts' | 'laptop' | 'smartphone' = 'all';
	let userFilter: string = '';
	let deviceSearch = '';
	let showAllMaintenance = false;
	let showAllIncidents = false;

	$: droneName =
		drone?.name ||
		(drone?.line && drone?.number ? `${drone.line}-${drone.number}` : 'Unknown Drone');
	$: droneDevice =
		networkDevices?.find(
			(device) => device.deviceType === 'drone' && device.interfaceRole === 'primary'
		) || null;
	$: ftsDevice =
		networkDevices?.find(
			(device) => device.deviceType === 'drone' && device.interfaceRole === 'secondary'
		) || null;
	$: ftsName = ftsDevice?.name?.includes('FTS') ? ftsDevice.name : `${droneName} FTS`;
	$: droneOnline = droneDevice
		? droneDevice.lastOnline
			? isDeviceOnline(droneDevice.lastOnline)
			: false
		: false;
	$: flightHoursText = drone?.totalFlightTimeInSeconds
		? formatFlightTime(drone.totalFlightTimeInSeconds)
		: '0h 0m';
	$: laptopDevices =
		networkDevices && selectedLaptopUserEmail
			? networkDevices
					.filter((device) => device.email === selectedLaptopUserEmail && isLaptopDevice(device))
					.slice(0, 2)
			: [];
	$: smartphoneDevices =
		networkDevices && selectedSmartphoneUserEmail
			? networkDevices
					.filter(
						(device) => device.email === selectedSmartphoneUserEmail && isSmartphoneDevice(device)
					)
					.slice(0, 2)
			: [];
	$: recentMaintenanceHistory = maintenanceHistory?.slice(0, 5) || [];
	$: recentStatusLogs = statusLogs?.slice(0, 5) || [];
	$: recentFlights = lastFlights?.slice(0, 5) || [];
	$: upcomingFlights = nextFlights?.slice(0, 5) || [];
	$: recentIncidents = lastIncidents?.slice(0, 5) || [];

	function deviceCategory(d: NetworkDevice): 'drone' | 'fts' | 'laptop' | 'smartphone' {
		if (d.deviceType === 'drone' && d.interfaceRole === 'secondary') return 'fts';
		if (d.deviceType === 'drone') return 'drone';
		if (isLaptopDevice(d)) return 'laptop';
		if (isSmartphoneDevice(d)) return 'smartphone';
		return 'laptop';
	}

	$: filteredDevices = (networkDevices || [])
		.filter((d) => (deviceTypeFilter === 'all' ? true : deviceCategory(d) === deviceTypeFilter))
		.filter((d) => (userFilter ? d.email === userFilter : true))
		.filter((d) => {
			const q = deviceSearch.trim().toLowerCase();
			if (!q) return true;
			return (
				(d.name || '').toLowerCase().includes(q) ||
				(d.ipAddress || '').toLowerCase().includes(q) ||
				(d.deviceType || '').toLowerCase().includes(q) ||
				(d.interfaceRole || '').toLowerCase().includes(q)
			);
		});

	$: visibleMaintenance = showAllMaintenance ? maintenanceHistory : recentMaintenanceHistory;
	$: visibleIncidents = showAllIncidents ? lastIncidents : recentIncidents;

	async function loadUsers() {
		if (!mounted) return;

		try {
			const fetchedUsers = await fetchUsers();
			if (mounted) users = fetchedUsers;
		} catch (error) {
			if (mounted) {
				users = [];
			}
			throw error;
		}
	}

	async function loadAllNetworkDevices() {
		if (!mounted || !drone) return;

		try {
			const droneName = drone.name || `${drone.line}-${drone.number}`;
			let allDevices = [];
			try {
				const allDevicesFromApi = await fetchNetworkDevices(null, null);
				if (mounted) allDevices.push(...allDevicesFromApi);
			} catch (error) {}

			if (!mounted) return;

			try {
				const droneDevices = await fetchNetworkDevices(droneName, null);
				if (mounted) allDevices.push(...droneDevices);
			} catch (error) {}

			if (!mounted) return;

			if (users && users.length > 0) {
				const userDevicesPromises = users.map(async (user) => {
					try {
						return await fetchNetworkDevices(null, user.email);
					} catch (error) {
						return [];
					}
				});

				try {
					const userDevicesArrays = await Promise.all(userDevicesPromises);
					if (mounted) {
						const userDevices = userDevicesArrays.flat();
						allDevices.push(...userDevices);
					}
				} catch (error) {}
			}

			if (mounted) {
				const uniqueDevices = allDevices.filter(
					(device, index, self) => index === self.findIndex((d) => d.id === device.id)
				);
				networkDevices = uniqueDevices;
			}
		} catch (error) {
			if (mounted) networkDevices = [];
			throw error;
		}
	}

	async function loadDroneData() {
		try {
			loading = true;
			error = '';

			const gliders = await fetchGliders();
			if (!mounted) return;

			drone =
				gliders.find((g) => g.name === droneId || `${g.line}-${g.number}` === droneId) || null;
			if (!drone) {
				if (mounted) {
					error = `Drone ${droneId} not found`;
					loading = false;
				}
				return;
			}

			try {
				await loadUsers();
			} catch (err) {
				if (mounted) toast.error('Failed to load users');
			}

			if (!mounted) return;

			const results = await Promise.allSettled([
				loadAllNetworkDevices(),
				loadMaintenanceHistory(),
				loadStatusLogs(),
				loadFlightData(),
				loadIncidents(),
				loadSoftwareVersions()
			]);

			if (mounted) {
				results.forEach((result, index) => {
					if (result.status === 'rejected') {
						const sections = [
							'Network Devices',
							'Maintenance',
							'Status Logs',
							'Flights',
							'Incidents',
							'Software Versions'
						];
						toast.error(`Failed to load ${sections[index]}`);
					}
				});
			}
		} catch (err) {
			if (mounted) {
				error = 'Failed to load drone data';
				toast.error('Failed to load drone data');
			}
		} finally {
			if (mounted) {
				loading = false;
			}
		}
	}

	async function loadMaintenanceHistory() {
		if (!drone || !mounted) return;

		try {
			if (mounted) isLoadingMaintenance = true;
			const maintenances = await fetchMaintenancesByGliderId(drone.id);
			if (!mounted) return;
			maintenanceHistory = maintenances.sort(
				(a, b) => new Date(b.dueDate).getTime() - new Date(a.dueDate).getTime()
			);
		} catch (error) {
			if (mounted) maintenanceHistory = [];
		} finally {
			if (mounted) isLoadingMaintenance = false;
		}
	}

	async function loadStatusLogs() {
		if (!drone?.name || !mounted) return;

		try {
			if (mounted) isLoadingStatusLogs = true;
			const logs = await fetchGliderStatusLogs(drone.name);
			if (!mounted) return;
			const filteredLogs = logs
				.filter((log) => log.oldGliderStatus?.id !== log.newGliderStatus?.id)
				.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
			statusLogs = filteredLogs.slice(0, 5);
		} catch (error) {
			if (mounted) statusLogs = [];
		} finally {
			if (mounted) isLoadingStatusLogs = false;
		}
	}

	async function loadFlightData() {
		if (!drone?.name || !mounted) return;

		try {
			if (mounted) isLoadingFlights = true;
			const flights = await fetchFlightsByGlider(drone.name);
			if (!mounted) return;

			totalFlights = flights.length;

			if (flights.length > 0) {
				const now = Date.now() / 1000;
				const sortedFlights = flights.sort((a, b) => b.start_time - a.start_time);

				lastFlights = sortedFlights.filter((f) => f.start_time < now).slice(0, 5);

				nextFlights = sortedFlights.filter((f) => f.start_time >= now).slice(0, 5);
			}
		} catch (error) {
			if (mounted) {
				totalFlights = 0;
				lastFlights = [];
				nextFlights = [];
			}
		} finally {
			if (mounted) isLoadingFlights = false;
		}
	}

	async function loadIncidents() {
		if (!mounted) return;

		try {
			if (mounted) isLoadingIncidents = true;
			const incidents = await fetchIncidents();
			if (!mounted) return;
			lastIncidents = incidents;
		} catch (error) {
			if (mounted) lastIncidents = [];
		} finally {
			if (mounted) isLoadingIncidents = false;
		}
	}

	function openEditModal() {
		showEditModal = true;
	}

	function closeEditModal() {
		showEditModal = false;
	}

	async function handleEditSuccess() {
		showSuccessToast = true;
		closeEditModal();
		await loadDroneData();
		setTimeout(() => (showSuccessToast = false), 3000);
	}

	function openRequestMaintenanceModal() {
		if (!drone) {
			toast.error('Drone data not available');
			return;
		}
		showRequestMaintenanceModal = true;
	}

	function closeRequestMaintenanceModal() {
		showRequestMaintenanceModal = false;
	}

	async function handleMaintenanceRequest(event: CustomEvent<UnscheduledMaintenanceRequest>) {
		const payload = event.detail;

		try {
			isSubmittingMaintenance = true;
			const response = await requestUnscheduledMaintenance(payload);

			if (response && response.message) {
				toast.success('Unscheduled maintenance notification sent successfully');
			} else {
				toast.success('Maintenance request submitted successfully');
			}

			closeRequestMaintenanceModal();
		} catch (error) {
			console.error('Error requesting maintenance:', error);
			toast.error('Error requesting maintenance');
		} finally {
			isSubmittingMaintenance = false;
		}
	}

	async function loadSoftwareVersions() {
		if (!mounted) return;

		try {
			loadingSoftwareVersions = true;

			const [jetsonVersions, autopilotVersions, ftsPixhawkVersions, ftsRaspiVersions] =
				await Promise.all([
					getSoftwareVersionsByTypeId(2), // Jetson
					getSoftwareVersionsByTypeId(1), // Autopilot
					getSoftwareVersionsByTypeId(5), // FTS Pixhawk
					getSoftwareVersionsByTypeId(4) // FTS Raspi
				]);

			if (mounted) {
				softwareVersions = {
					jetson: jetsonVersions,
					autopilot: autopilotVersions,
					ftsPixhawk: ftsPixhawkVersions,
					ftsRaspi: ftsRaspiVersions
				};
			}
		} catch (error) {
			if (mounted) {
				console.error('Error loading software versions:', error);
				toast.error('Failed to load software versions');
			}
		} finally {
			if (mounted) {
				loadingSoftwareVersions = false;
			}
		}
	}

	function handleVersionSelected(event: CustomEvent) {
		versionUpdateData = event.detail;
		showConfirmVersionModal = true;
	}

	async function confirmVersionUpdate() {
		if (!versionUpdateData || !drone) return;

		try {
			isUpdatingVersion = true;

			const updatedGlider = await updateGliderSoftwareVersion(
				drone.id,
				versionUpdateData.updateField,
				versionUpdateData.version.id
			);

			if (updatedGlider) {
				drone = updatedGlider;
				toast.success(`${versionUpdateData.componentType} version updated successfully`);
				showConfirmVersionModal = false;
				versionUpdateData = null;
			} else {
				toast.error('Failed to update software version');
			}
		} catch (error) {
			console.error('Error updating software version:', error);
			toast.error('Error updating software version');
		} finally {
			isUpdatingVersion = false;
		}
	}

	function cancelVersionUpdate() {
		showConfirmVersionModal = false;
		versionUpdateData = null;
	}

	function isLaptopDevice(device: NetworkDevice): boolean {
		return device.deviceType === 'macbook' || device.deviceType === 'linux_computer';
	}

	function isSmartphoneDevice(device: NetworkDevice): boolean {
		return device.deviceType === 'iphone' || device.deviceType === 'android';
	}

	function getStatusColor(status: unknown): string {
		if (!status) return 'gray';
		const statusName = (status.name || status.toString()).toLowerCase();
		return STATUS_COLORS[statusName] || 'gray';
	}

	function getOnlineStatusClass(isOnline: boolean): string {
		return isOnline ? 'bg-green-500' : 'bg-gray-400';
	}

	function isDeviceOnline(lastOnline: string): boolean {
		return lastOnline.includes('sec') || lastOnline.includes('min');
	}

	function getOnlineStatusText(lastOnline: string): string {
		return isDeviceOnline(lastOnline) ? 'ONLINE' : lastOnline;
	}

	onMount(() => {
		const timeoutId = setTimeout(() => {
			if (mounted && loading) {
				loading = false;
				error = 'Loading timeout - please refresh the page';
			}
		}, 30000);

		loadDroneData().finally(() => {
			clearTimeout(timeoutId);
		});
	});
	onDestroy(() => {
		mounted = false;
	});
</script>

{#snippet loadingSpinner()}
	<div class="flex items-center justify-center py-8" role="status" aria-live="polite">
		<div
			class="h-5 w-5 rounded-full border-b-2 border-primary-600 motion-safe:animate-spin motion-reduce:animate-none"
		></div>
		<span class="ui-text ml-2 text-gray-600">{TEXTS.loading}</span>
	</div>
{/snippet}

{#snippet emptyState(message)}
	<div class="py-8 text-center text-gray-500">{message || TEXTS.empty}</div>
{/snippet}

{#snippet metricTile(label, value, icon = null)}
	<div class="rounded-lg border bg-white p-6 shadow-sm">
		<div class="flex items-start justify-between">
			<div class="min-w-0 flex-1">
				<p class="mb-2 text-xs font-medium text-gray-500">{label}</p>
				<p class="break-words text-base font-semibold text-gray-900">{value || TEXTS.empty}</p>
			</div>
			{#if icon}
				<div class="ml-4 flex-shrink-0 text-gray-400">
					{@render icon()}
				</div>
			{/if}
		</div>
	</div>
{/snippet}

{#snippet onlineIndicator(isOnline, text)}
	<div class="flex items-center gap-1">
		<div class="h-2 w-2 rounded-full {getOnlineStatusClass(isOnline)}"></div>
		<span class="text-sm {isOnline ? 'font-medium text-green-600' : 'text-gray-500'}">{text}</span>
	</div>
{/snippet}

{#snippet networkDeviceTable(devices)}
	{#if devices.length > 0}
		<div class="space-y-3">
			{#each devices as device}
				<div class="rounded-lg border bg-gray-50 p-3">
					<div class="mb-2 flex items-center justify-between">
						<span class="rounded bg-white px-2 py-1 font-mono text-sm text-gray-700"
							>{device.ipAddress}</span
						>
						{@render onlineIndicator(
							isDeviceOnline(device.lastOnline),
							isDeviceOnline(device.lastOnline) ? TEXTS.online : device.lastOnline
						)}
					</div>
					<div class="truncate text-sm text-gray-600">{device.name}</div>
				</div>
			{/each}
		</div>
	{:else}
		<div class="text-gray-500">{TEXTS.noDeviceFound}</div>
	{/if}
{/snippet}

<svelte:head>
	<title>Drone {droneId} Dashboard</title>
</svelte:head>
<Button
	color="light"
	size="xs"
	class="ui-btn-focus mb-3 flex items-center gap-1 text-gray-600 hover:bg-gray-50 hover:text-gray-900"
	on:click={() => window.history.back()}
	aria-label={TEXTS.backToFleet}
>
	<ArrowLeftOutline class="h-4 w-4" />
	{TEXTS.backToFleet}
</Button>

<div class="min-h-screen">
	{#if drone}
		<div class="border-b border-gray-200 bg-white">
			<div class="w-full px-4 py-3 lg:px-8">
				<div class="flex flex-wrap items-center justify-between gap-3">
					<div class="flex min-w-0 items-center gap-2">
						<h1 class="truncate text-lg font-semibold text-gray-900" title={droneName}>
							{droneName}
						</h1>
						<GliderStatusBadge
							glider={drone}
							onStatusUpdate={(updatedGlider) => {
								drone = updatedGlider;
								loadStatusLogs();
							}}
						/>
						{#if droneDevice}
							<div class="ml-2 inline-flex items-center rounded bg-gray-100 px-2 py-0.5">
								<span class="sr-only">Connection</span>
								<span
									class="mr-1 inline-block h-2 w-2 rounded-full {droneOnline
										? 'bg-green-500'
										: 'bg-red-500'}"
								></span>
								<span class="ui-text-xs text-gray-700"
									>{droneOnline ? TEXTS.connected : TEXTS.offline}</span
								>
							</div>
						{/if}
					</div>
					<div class="flex flex-wrap items-center gap-4">
						<!-- Stats Cards -->
						<div class="flex items-center gap-3"></div>
					</div>
				</div>
			</div>
		</div>
	{/if}

	<div class="w-full px-4 py-4 lg:px-8">
		{#if loading}
			<div class="flex items-center justify-center py-12" role="status" aria-live="polite">
				<div
					class="h-6 w-6 rounded-full border-b-2 border-primary-600 motion-safe:animate-spin motion-reduce:animate-none"
				></div>
				<span class="ui-text ml-2 text-gray-600">{TEXTS.loadingDroneData}</span>
			</div>
		{:else if error}
			<Alert color="red" class="mb-3 text-sm">
				<span class="font-medium">Error:</span>
				{error}
			</Alert>

			<div class="mb-6">
				<SectionCard title="Flights & Maintenance">
					<div class="grid grid-cols-1 gap-3 sm:grid-cols-3">
						<div class="rounded-lg border bg-white p-4">
							<div class="text-xs text-gray-500">Total Flights</div>
							<div class="text-xl font-semibold">{totalFlights}</div>
						</div>
						<div class="rounded-lg border bg-white p-4">
							<div class="text-xs text-gray-500">Total Flight Hours</div>
							<div class="text-xl font-semibold">{flightHoursText}</div>
						</div>
						<div class="rounded-lg border bg-white p-4">
							<div class="text-xs text-gray-500">Flight Hours Since Last Maintenance</div>
							<div class="text-xl font-semibold">
								{formatFlightTime(drone.totalFlightTimeSinceLastMaintenanceInSeconds || 0)}
							</div>
						</div>
					</div>
					<svelte:fragment slot="actions">
						<Button
							color="primary"
							size="sm"
							class="ui-btn-focus"
							on:click={openRequestMaintenanceModal}
							aria-label="Request Unscheduled Maintenance"
						>
							<CogOutline class="mr-2 h-4 w-4" />
							Request Unscheduled Maintenance
						</Button>
					</svelte:fragment>
				</SectionCard>
			</div>
		{:else if drone}
			<div class="mb-6">
				<div class="rounded border bg-white">
					<div class="border-b bg-gray-50 px-4 py-2">
						<h2 class="text-lg font-semibold text-gray-900">{TEXTS.droneInformation}</h2>
					</div>
					<div class="p-4">
						<div class="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-3">
							<div class="space-y-0.5">
								<dt class="ui-text-xs text-gray-500">{TEXTS.vpnIp}</dt>
								<dd class="ui-mono truncate text-sm text-gray-900" title={drone.vpnIp || 'N/A'}>
									{drone.vpnIp || 'N/A'}
								</dd>
							</div>
							<div class="space-y-0.5">
								<dt class="ui-text-xs text-gray-500">{TEXTS.region}</dt>
								<dd
									class="truncate text-sm text-gray-900"
									title={drone.region?.country || drone.region?.name || drone.region || 'N/A'}
								>
									{drone.region?.country || drone.region?.name || drone.region || 'N/A'}
								</dd>
							</div>
							<div class="space-y-0.5">
								<dt class="ui-text-xs text-gray-500">{TEXTS.company}</dt>
								<dd
									class="truncate text-sm text-gray-900"
									title={drone.company?.name || drone.company || 'N/A'}
								>
									{drone.company?.name || drone.company || 'N/A'}
								</dd>
							</div>
							<div class="space-y-0.5">
								<dt class="ui-text-xs text-gray-500">{TEXTS.manufacturingDate}</dt>
								<dd class="text-sm text-gray-900">
									{formatDate(drone.manufacturingDate) || 'N/A'}
								</dd>
							</div>

							<div class="space-y-0.5">
								<dt class="ui-text-xs text-gray-500">{TEXTS.pixhawkUuid}</dt>
								<dd
									class="ui-mono truncate text-sm text-gray-900"
									title={drone.pixhawkUuid || 'N/A'}
								>
									{drone.pixhawkUuid || 'N/A'}
								</dd>

								{#if drone.designDeviation}
									<div class="space-y-0.5">
										<dt class="ui-text-xs text-gray-500">Design Deviations</dt>
										<dd
											class="whitespace-pre-line text-sm text-gray-900"
											title={drone.designDeviation}
										>
											{drone.designDeviation}
										</dd>
									</div>
								{/if}
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="mb-6">
				<SectionCard title="Network">
					<div class="grid grid-cols-1 gap-3 p-3 sm:grid-cols-2">
						{#if droneDevice}
							<div class="rounded border bg-white p-3">
								<div class="mb-2 flex items-center justify-between">
									<h4 class="text-base font-semibold text-gray-900">Main IP</h4>
									<div class="h-4 w-4 rounded-full {getOnlineStatusClass(droneOnline)}"></div>
								</div>
								<div class="mb-6 text-sm font-medium text-primary-600">{droneName}</div>
								<div class="space-y-4">
									<div class="flex items-center justify-between">
										<span class="text-sm text-gray-500">IP:</span>
										<span class="rounded bg-gray-100 px-2 py-1 font-mono text-xs"
											>{droneDevice.ipAddress}</span
										>
									</div>
									{@render onlineIndicator(
										droneOnline,
										getOnlineStatusText(droneDevice.lastOnline)
									)}
								</div>
							</div>
						{:else}
							<div class="rounded border bg-white p-3">
								<div class="mb-2 flex items-center justify-between">
									<h4 class="text-base font-semibold text-gray-900">Main IP</h4>
									<div class="h-4 w-4 rounded-full {getOnlineStatusClass(false)}"></div>
								</div>
								<div class="text-sm text-gray-500">{TEXTS.noDeviceFound}</div>
							</div>
						{/if}

						{#if ftsDevice}
							<div class="rounded border bg-white p-3">
								<div class="mb-2 flex items-center justify-between">
									<h4 class="text-base font-semibold text-gray-900">FTS IP</h4>
									<div
										class="h-4 w-4 rounded-full {getOnlineStatusClass(
											isDeviceOnline(ftsDevice.lastOnline)
										)}"
									></div>
								</div>
								<div class="mb-6 text-sm font-medium text-primary-600">{ftsName}</div>
								<div class="space-y-4">
									<div class="flex items-center justify-between">
										<span class="text-sm text-gray-500">IP:</span>
										<span class="rounded bg-gray-100 px-2 py-1 font-mono text-xs"
											>{ftsDevice.ipAddress}</span
										>
									</div>
									{@render onlineIndicator(
										isDeviceOnline(ftsDevice.lastOnline),
										getOnlineStatusText(ftsDevice.lastOnline)
									)}
								</div>
							</div>
						{:else}
							<div class="rounded border bg-white p-3">
								<div class="mb-2 flex items-center justify-between">
									<h4 class="text-base font-semibold text-gray-900">FTS IP</h4>
									<div class="h-4 w-4 rounded-full {getOnlineStatusClass(false)}"></div>
								</div>
								<div class="text-sm text-gray-500">{TEXTS.noDeviceFound}</div>
							</div>
						{/if}

						<div class="rounded border bg-white p-3">
							<div class="mb-2 flex items-center justify-between">
								<h4 class="text-base font-semibold text-gray-900">Operator Laptops</h4>
								<div class="h-4 w-4 rounded-full {getOnlineStatusClass(false)}"></div>
							</div>
							<div class="mb-6">
								<Select
									bind:value={selectedLaptopUserEmail}
									size="md"
									placeholder={TEXTS.selectUser}
								>
									{#each users as user}
										<option value={user.email}>{user.email}</option>
									{/each}
								</Select>
							</div>
							{#if selectedLaptopUserEmail}
								{@render networkDeviceTable(laptopDevices)}
							{:else}
								<div class="text-sm text-gray-500">{TEXTS.selectUser}</div>
							{/if}
						</div>

						<div class="rounded border bg-white p-3">
							<div class="mb-2 flex items-center justify-between">
								<h4 class="text-base font-semibold text-gray-900">Operator FTS Apps</h4>
								<div class="h-4 w-4 rounded-full {getOnlineStatusClass(false)}"></div>
							</div>
							<div class="mb-6">
								<Select
									bind:value={selectedSmartphoneUserEmail}
									size="md"
									placeholder={TEXTS.selectUser}
								>
									{#each users as user}
										<option value={user.email}>{user.email}</option>
									{/each}
								</Select>
							</div>
							{#if selectedSmartphoneUserEmail}
								{@render networkDeviceTable(smartphoneDevices)}
							{:else}
								<div class="text-sm text-gray-500">{TEXTS.selectUser}</div>
							{/if}
						</div>
					</div>
				</SectionCard>

				<div class="mb-6">
					<SectionCard title={TEXTS.softwareVersions}>
						<div class="overflow-auto">
							{#if loadingSoftwareVersions}
								<div class="flex items-center justify-center py-6" role="status" aria-live="polite">
									<div
										class="h-5 w-5 rounded-full border-b-2 border-primary-600 motion-safe:animate-spin motion-reduce:animate-none"
									></div>
									<span class="ui-text ml-2 text-gray-600">{TEXTS.loadingSoftwareVersions}</span>
								</div>
							{:else}
								<table class="w-full table-fixed">
									<thead class="ui-table-head ui-sticky-head">
										<tr>
											<th scope="col" class="ui-th w-1/4">{TEXTS.component}</th>
											<th scope="col" class="ui-th w-1/4">{TEXTS.current}</th>
											<th scope="col" class="ui-th w-1/4">{TEXTS.desired}</th>
											<th scope="col" class="ui-th w-1/4">Known Issues</th>
										</tr>
									</thead>
									<tbody>
										<SoftwareVersionManager
											{drone}
											componentType="jetson"
											availableVersions={softwareVersions.jetson}
											loading={isUpdatingVersion}
											on:versionSelected={handleVersionSelected}
										/>
										<SoftwareVersionManager
											{drone}
											componentType="autopilot"
											availableVersions={softwareVersions.autopilot}
											loading={isUpdatingVersion}
											on:versionSelected={handleVersionSelected}
										/>
										<SoftwareVersionManager
											{drone}
											componentType="ftsPixhawk"
											availableVersions={softwareVersions.ftsPixhawk}
											loading={isUpdatingVersion}
											on:versionSelected={handleVersionSelected}
										/>
										<SoftwareVersionManager
											{drone}
											componentType="ftsRaspi"
											availableVersions={softwareVersions.ftsRaspi}
											loading={isUpdatingVersion}
											on:versionSelected={handleVersionSelected}
										/>
									</tbody>
								</table>
							{/if}
						</div>
					</SectionCard>
				</div>

				<div class="mb-12">
					<SectionCard title={TEXTS.maintenanceHistory}>
						<svelte:fragment slot="actions">
							<button
								class="ui-btn-focus ui-text-xs underline"
								on:click={() => (showAllMaintenance = !showAllMaintenance)}
							>
								{showAllMaintenance
									? TEXTS.showLess
									: `${TEXTS.showAll} (${maintenanceHistory.length})`}
							</button>
						</svelte:fragment>
						{#if isLoadingMaintenance}
							{@render loadingSpinner()}
						{:else if recentMaintenanceHistory.length > 0}
							<div class="overflow-x-auto">
								<Table>
									<TableHead>
										<TableHeadCell>Due Date</TableHeadCell>
										<TableHeadCell>Completed</TableHeadCell>
										<TableHeadCell>Type</TableHeadCell>
										<TableHeadCell>Staff</TableHeadCell>
										<TableHeadCell>Notes</TableHeadCell>
									</TableHead>
									<TableBody>
										{#each visibleMaintenance as maintenance}
											<TableBodyRow>
												<TableBodyCell>{formatDate(maintenance.dueDate)}</TableBodyCell>
												<TableBodyCell>
													{#if maintenance.completedDate}
														<Badge color="green" size="xs"
															>{formatDate(maintenance.completedDate)}</Badge
														>
													{:else}
														<Badge color="yellow" size="xs">Pending</Badge>
													{/if}
												</TableBodyCell>
												<TableBodyCell
													>{maintenance.maintenanceType?.name || TEXTS.empty}</TableBodyCell
												>
												<TableBodyCell>{maintenance.maintenanceStaff || TEXTS.empty}</TableBodyCell>
												<TableBodyCell class="max-w-xs">
													<div class="truncate" title={maintenance.notes || ''}>
														{maintenance.notes || TEXTS.empty}
													</div>
												</TableBodyCell>
											</TableBodyRow>
										{/each}
									</TableBody>
								</Table>
							</div>
						{:else}
							{@render emptyState(TEXTS.noMaintenance)}
						{/if}
					</SectionCard>
				</div>

				<div class="mb-12">
					<SectionCard title={TEXTS.recentIncidents}>
						<svelte:fragment slot="actions">
							<button
								class="ui-btn-focus ui-text-xs underline"
								on:click={() => (showAllIncidents = !showAllIncidents)}
							>
								{showAllIncidents ? TEXTS.showLess : `${TEXTS.showAll} (${lastIncidents.length})`}
							</button>
						</svelte:fragment>
						{#if isLoadingIncidents}
							{@render loadingSpinner()}
						{:else if recentIncidents.length > 0}
							<div class="overflow-x-auto">
								<Table>
									<TableHead>
										<TableHeadCell>Name</TableHeadCell>
										<TableHeadCell>Damage</TableHeadCell>
										<TableHeadCell>Description</TableHeadCell>
										<TableHeadCell>Injuries</TableHeadCell>
									</TableHead>
									<TableBody>
										{#each visibleIncidents as incident}
											<TableBodyRow>
												<TableBodyCell class="font-medium"
													>{incident.name || TEXTS.empty}</TableBodyCell
												>
												<TableBodyCell>
													{#if incident.aircraft_damage}
														<Badge color="red" size="xs">{incident.aircraft_damage}</Badge>
													{:else}
														{TEXTS.empty}
													{/if}
												</TableBodyCell>
												<TableBodyCell class="max-w-xs">
													<div class="truncate" title={incident.description || ''}>
														{incident.description || TEXTS.empty}
													</div>
												</TableBodyCell>
												<TableBodyCell>
													{#if incident.injuries}
														<Badge color="red" size="xs">{incident.injuries}</Badge>
													{:else}
														<Badge color="green" size="xs">{TEXTS.none}</Badge>
													{/if}
												</TableBodyCell>
											</TableBodyRow>
										{/each}
									</TableBody>
								</Table>
							</div>
						{:else}
							{@render emptyState(TEXTS.noIncidents)}
						{/if}
					</SectionCard>
				</div>

				<div class="mb-12 grid grid-cols-1 gap-12 lg:grid-cols-2">
					<div class="rounded-lg border bg-white shadow-sm">
						<div class="flex items-center justify-between border-b bg-gray-50 px-8 py-6">
							<h2 class="text-lg font-semibold text-gray-900">Recent Flights</h2>
						</div>
						{#if isLoadingFlights}
							{@render loadingSpinner()}
						{:else if recentFlights.length > 0}
							<div class="space-y-6 p-8">
								{#each recentFlights as flight}
									<div class="rounded-lg border p-4 hover:bg-gray-50">
										<div class="mb-2 flex items-center justify-between">
											<span class="text-sm font-medium">Flight #{flight.id}</span>
											<span class="text-xs text-gray-500">
												{new Date(flight.start_time * 1000).toLocaleDateString('en-GB', {
													timeZone: 'UTC'
												})} UTC
											</span>
										</div>
										<div class="grid grid-cols-2 gap-2 text-xs text-gray-600">
											<div>
												<span>Duration:</span>
												<span class="ml-1 font-medium">
													{Math.floor(flight.duration / 3600)}h {Math.floor(
														(flight.duration % 3600) / 60
													)}m
												</span>
											</div>
											<div>
												<span>Distance:</span>
												<span class="ml-1 font-medium"
													>{flight.distance_meters
														? `${Math.round(flight.distance_meters)}m`
														: TEXTS.empty}</span
												>
											</div>
										</div>
										{#if flight.start_location || flight.end_location}
											<div class="mt-2 text-xs text-gray-600">
												<span>Route:</span>
												<span class="ml-1 font-medium">
													{flight.start_location || TEXTS.empty} → {flight.end_location ||
														TEXTS.empty}
												</span>
											</div>
										{/if}
									</div>
								{/each}
							</div>
						{:else}
							{@render emptyState(TEXTS.noFlights)}
						{/if}
					</div>

					<div class="rounded-lg border bg-white shadow-sm">
						<div class="flex items-center justify-between border-b bg-gray-50 px-8 py-6">
							<h2 class="text-lg font-semibold text-gray-900">Scheduled Flights</h2>
						</div>
						{#if isLoadingFlights}
							{@render loadingSpinner()}
						{:else if upcomingFlights.length > 0}
							<div class="space-y-6 p-8">
								{#each upcomingFlights as flight}
									<div class="rounded-lg border p-4 hover:bg-gray-50">
										<div class="mb-2 flex items-center justify-between">
											<span class="text-sm font-medium">Flight #{flight.id}</span>
											<span class="text-xs text-gray-500">
												{new Date(flight.start_time * 1000).toLocaleDateString('en-GB', {
													timeZone: 'UTC'
												})} UTC
											</span>
										</div>
										<div class="grid grid-cols-2 gap-2 text-xs text-gray-600">
											<div>
												<span>Start:</span>
												<span class="ml-1 font-medium">
													{new Date(flight.start_time * 1000).toLocaleTimeString('en-GB', {
														hour: '2-digit',
														minute: '2-digit',
														timeZone: 'UTC'
													})}
												</span>
											</div>
											<div>
												<span>Duration:</span>
												<span class="ml-1 font-medium">
													{Math.floor(flight.duration / 3600)}h {Math.floor(
														(flight.duration % 3600) / 60
													)}m
												</span>
											</div>
										</div>
									</div>
								{/each}
							</div>
						{:else}
							{@render emptyState(TEXTS.noScheduledFlights)}
						{/if}
					</div>
				</div>

				<div class="mb-12">
					<div class="rounded-lg border bg-white shadow-sm">
						<div class="flex items-center justify-between border-b bg-gray-50 px-8 py-6">
							<h2 class="text-lg font-semibold text-gray-900">Status History</h2>
						</div>
						{#if isLoadingStatusLogs}
							{@render loadingSpinner()}
						{:else if recentStatusLogs.length > 0}
							<div class="overflow-x-auto">
								<Table>
									<TableHead>
										<TableHeadCell>Date</TableHeadCell>
										<TableHeadCell>User</TableHeadCell>
										<TableHeadCell>From</TableHeadCell>
										<TableHeadCell>To</TableHeadCell>
										<TableHeadCell>Note</TableHeadCell>
									</TableHead>
									<TableBody>
										{#each recentStatusLogs as log}
											<TableBodyRow>
												<TableBodyCell class="text-sm">
													{new Date(log.createdAt).toLocaleDateString('en-GB', { timeZone: 'UTC' })}
													UTC
													{new Date(log.createdAt).toLocaleTimeString('en-GB', {
														hour: '2-digit',
														minute: '2-digit',
														timeZone: 'UTC'
													})}
												</TableBodyCell>
												<TableBodyCell class="text-sm">{log.userEmail}</TableBodyCell>
												<TableBodyCell>
													{#if log.oldGliderStatus}
														<Badge color={getStatusColor(log.oldGliderStatus)} size="xs">
															{log.oldGliderStatus.name}
														</Badge>
													{:else}
														{TEXTS.empty}
													{/if}
												</TableBodyCell>
												<TableBodyCell>
													{#if log.newGliderStatus}
														<Badge color={getStatusColor(log.newGliderStatus)} size="xs">
															{log.newGliderStatus.name}
														</Badge>
													{:else}
														{TEXTS.empty}
													{/if}
												</TableBodyCell>
												<TableBodyCell class="max-w-xs">
													<div class="truncate" title={log.note || ''}>
														{log.note || TEXTS.empty}
													</div>
												</TableBodyCell>
											</TableBodyRow>
										{/each}
									</TableBody>
								</Table>
							</div>
						{:else}
							{@render emptyState(TEXTS.noStatusChanges)}
						{/if}
					</div>
				</div>
			</div>
		{/if}
	</div>
</div>

<EditDroneModal
	bind:showModal={showEditModal}
	{drone}
	onClose={closeEditModal}
	onSuccess={handleEditSuccess}
/>

<RequestMaintenanceModal
	bind:showModal={showRequestMaintenanceModal}
	data={{
		gliderName: drone?.name || '',
		userEmail: email
	}}
	submitting={isSubmittingMaintenance}
	on:submit={handleMaintenanceRequest}
	on:cancel={closeRequestMaintenanceModal}
/>

<ConfirmSoftwareVersionModal
	bind:showModal={showConfirmVersionModal}
	componentName={versionUpdateData?.componentType || ''}
	oldVersion={versionUpdateData?.oldVersion || ''}
	newVersion={versionUpdateData?.newVersion || ''}
	submitting={isUpdatingVersion}
	on:confirm={confirmVersionUpdate}
	on:cancel={cancelVersionUpdate}
/>

{#if showSuccessToast}
	<Toast color="green" position="top-right" class="mb-4">
		<svelte:fragment slot="icon">
			<CheckCircleOutline class="h-5 w-5" />
		</svelte:fragment>
		Drone information updated successfully!
	</Toast>
{/if}
