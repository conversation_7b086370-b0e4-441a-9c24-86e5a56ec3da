<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { Modal, Button } from 'flowbite-svelte';
	import { ExclamationCircleOutline } from 'flowbite-svelte-icons';

	export let showModal: boolean = false;
	export let componentName: string = '';
	export let oldVersion: string = '';
	export let newVersion: string = '';
	export let submitting: boolean = false;

	const dispatch = createEventDispatcher();

	function onConfirm() {
		dispatch('confirm');
	}

	function onCancel() {
		dispatch('cancel');
		showModal = false;
	}
</script>

<Modal bind:open={showModal} size="md" autoclose={false} class="w-full">
	<div class="p-6">
		<div class="mb-6 flex items-center gap-3">
			<div class="flex-shrink-0">
				<ExclamationCircleOutline class="h-8 w-8 text-yellow-500" />
			</div>
			<div>
				<h2 class="text-xl font-semibold text-gray-900">Confirm Software Version Change</h2>
				<p class="mt-1 text-gray-600">
					This will update the desired software version for this component.
				</p>
			</div>
		</div>

		<div class="mb-6 rounded-lg bg-gray-50 p-4">
			<div class="space-y-3">
				<div>
					<span class="text-sm font-medium text-gray-500">Component:</span>
					<span class="ml-2 text-base font-semibold text-gray-900">{componentName}</span>
				</div>
				<div>
					<span class="text-sm font-medium text-gray-500">Current desired version:</span>
					<span class="ml-2 font-mono text-base text-gray-900">{oldVersion}</span>
				</div>
				<div>
					<span class="text-sm font-medium text-gray-500">New desired version:</span>
					<span class="ml-2 font-mono text-base font-semibold text-primary-600">{newVersion}</span>
				</div>
			</div>
		</div>

		<div class="mb-6 rounded-lg border border-blue-200 bg-blue-50 p-4">
			<div class="flex items-start gap-2">
				<div class="mt-0.5 flex-shrink-0">
					<div class="h-4 w-4 rounded-full bg-blue-500"></div>
				</div>
				<div class="text-sm text-blue-800">
					<p class="mb-1 font-medium">Important:</p>
					<p>
						This only updates the desired version in the system. The actual software update must be
						performed manually on the device.
					</p>
				</div>
			</div>
		</div>

		<div class="flex justify-end gap-3">
			<Button color="light" on:click={onCancel} disabled={submitting}>Cancel</Button>
			<Button color="primary" on:click={onConfirm} disabled={submitting} class="min-w-[100px]">
				{#if submitting}
					<div class="flex items-center gap-2">
						<div
							class="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"
						></div>
						Updating...
					</div>
				{:else}
					Confirm Update
				{/if}
			</Button>
		</div>
	</div>
</Modal>
