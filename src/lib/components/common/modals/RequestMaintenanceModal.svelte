<script lang="ts">
	import { createEventDispatcher, onMount } from 'svelte';
	import { Modal, Label, Button, Textarea, Radio } from 'flowbite-svelte';
	import { userProfile } from '$lib/stores';
	import { get } from 'svelte/store';
	import {
		MAINTENANCE_REASONS,
		type MaintenanceReason,
		type RequestMaintenanceDialogData
	} from '$lib/types/maintenance-request.types';

	export let showModal: boolean = false;
	export let data: RequestMaintenanceDialogData;
	export let submitting: boolean = false;

	const dispatch = createEventDispatcher();

	let selectedReason: MaintenanceReason = 'Incident';
	let extraNotes: string = '';

	$: userEmail = get(userProfile)?.email || data?.userEmail || '';

	function onCancel() {
		showModal = false;
		dispatch('cancel');
	}

	function onSubmit() {
		if (!selectedReason) {
			return;
		}

		const payload = {
			glider_name: data.gliderName,
			user_email: userEmail,
			maintenance_reasons: selectedReason,
			extra_notes: extraNotes.trim()
		};

		dispatch('submit', payload);
	}

	$: if (showModal) {
		selectedReason = 'Incident';
		extraNotes = '';
	}
</script>

<Modal bind:open={showModal} size="md" autoclose={false} class="w-full">
	<div class="p-6">
		<h2 class="mb-6 text-xl font-semibold text-gray-900">Request Unscheduled Maintenance</h2>

		<div class="space-y-6">
			<div class="rounded-lg bg-gray-50 p-4">
				<p class="text-sm text-gray-600">
					Glider: <span class="font-medium text-gray-900">{data?.gliderName || 'N/A'}</span>
				</p>
				<p class="text-sm text-gray-600">
					Requested by: <span class="font-medium text-gray-900">{userEmail || 'N/A'}</span>
				</p>
			</div>
			<div>
				<Label class="mb-3 block text-base font-medium text-gray-900">Maintenance reason:</Label>
				<div class="space-y-2">
					{#each MAINTENANCE_REASONS as reason}
						<div class="flex items-center">
							<Radio
								bind:group={selectedReason}
								value={reason}
								class="text-primary-600 focus:ring-primary-500"
							/>
							<Label class="ml-2 text-sm text-gray-700">{reason}</Label>
						</div>
					{/each}
				</div>
			</div>

			<div>
				<Label for="extra-notes" class="mb-3 block text-base font-medium text-gray-900"
					>Extra notes:</Label
				>
				<Textarea
					id="extra-notes"
					bind:value={extraNotes}
					placeholder="Please provide any details about tasks, checklists, or other info..."
					rows="5"
					class="w-full"
				/>
			</div>
		</div>
		<div class="mt-8 flex justify-end gap-3">
			<Button color="light" on:click={onCancel} disabled={submitting}>Cancel</Button>
			<Button
				color="primary"
				on:click={onSubmit}
				disabled={!selectedReason || submitting}
				class="min-w-[100px]"
			>
				{#if submitting}
					<div class="flex items-center gap-2">
						<div
							class="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"
						></div>
						Submitting...
					</div>
				{:else}
					Submit
				{/if}
			</Button>
		</div>
	</div>
</Modal>
