<script lang="ts">
	import { onMount, createEventDispatcher, tick } from 'svelte';
	import { <PERSON>ge, Modal, Button, Label, Textarea } from 'flowbite-svelte';
	import { ChevronDownOutline } from 'flowbite-svelte-icons';
	import { toast } from 'svelte-sonner';
	import { get } from 'svelte/store';
	import { userProfile } from '$lib/stores';
	import {
		updateDroneStatus,
		getGliderStatusLogs,
		getAvailableStatuses,
		getStatusColor,
		getFlowbiteColor,
		getTextColor,
		type GliderStatus,
		type GliderStatusLog,
		type UpdateStatusPayload
	} from '$lib/services/status.service';
	import { fetchGliderById, type Glider } from '$lib/services/gliders.service';
	import type { Glider } from '$lib/services/gliders.service';

	export let glider: Glider | null;
	export let onStatusUpdate: (updatedGlider: Glider) => void = () => {};

	const dispatch = createEventDispatcher();

	let updating = false;
	let dropdownOpen = false;
	let showConfirmModal = false;
	let selectedStatus: GliderStatus | null = null;
	let note = '';
	let statusHistory: GliderStatusLog[] = [];
	let loadingHistory = false;
	let tooltipVisible = false;
	let placeAbove = false;
	let availableStatuses: GliderStatus[] = [];
	let loadingStatuses = false;

	let dropdownElement: HTMLDivElement;
	let buttonElement: HTMLButtonElement;
	let tooltipElement: HTMLDivElement;

	$: currentStatus = glider?.gliderStatus as GliderStatus;
	$: currentStatusName = currentStatus?.name || 'Unknown';
	$: currentStatusColor = getStatusColor(currentStatus);
	$: currentTextColor = getTextColor(currentStatusColor);
	$: badgeColor = getFlowbiteColor(currentStatus);
	$: displayText = updating ? 'Updating...' : currentStatusName;

	onMount(async () => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				dropdownElement &&
				!dropdownElement.contains(event.target as Node) &&
				buttonElement &&
				!buttonElement.contains(event.target as Node)
			) {
				dropdownOpen = false;
			}
		};

		document.addEventListener('click', handleClickOutside);

		await loadAvailableStatuses();

		return () => document.removeEventListener('click', handleClickOutside);
	});

	async function loadAvailableStatuses() {
		loadingStatuses = true;
		try {
			availableStatuses = await getAvailableStatuses();
		} catch (error) {
			console.error('Error loading available statuses:', error);
			availableStatuses = [];
		} finally {
			loadingStatuses = false;
		}
	}

	function toggleDropdown() {
		if (updating || !glider) return;
		dropdownOpen = !dropdownOpen;
	}

	function selectStatus(status: GliderStatus) {
		selectedStatus = status;
		note = '';
		dropdownOpen = false;
		showConfirmModal = true;
	}

	function cancelStatusChange() {
		showConfirmModal = false;
		selectedStatus = null;
		note = '';
	}

	async function confirmStatusChange() {
		if (!selectedStatus || !note.trim()) {
			toast.error('Note is required');
			return;
		}

		if (!glider) {
			toast.error('Glider not found');
			return;
		}

		if (selectedStatus.name === currentStatusName) {
			toast.error('New status cannot be the same as current status');
			return;
		}

		const profile = get(userProfile);
		const userEmail = profile?.email;
		if (!userEmail) {
			toast.error('Failed to get user email');
			return;
		}

		updating = true;
		showConfirmModal = false;

		try {
			const payload: UpdateStatusPayload = {
				gliderName: glider.name,
				status: selectedStatus.name, // API function will format this to slug
				note: note.trim(),
				userEmail: userEmail
			};

			const response = await updateDroneStatus(payload);

			const updatedGlider = await fetchGliderById(glider.id);

			if (updatedGlider) {
				onStatusUpdate(updatedGlider);
				dispatch('statusUpdated', updatedGlider);
			} else {
				throw new Error('Failed to reload glider data after status update');
			}

			await loadStatusHistory();

			toast.success(`Glider status updated to "${selectedStatus.name}"`);
		} catch (error) {
			console.error('Error updating glider status:', error);

			const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

			if (errorMessage.includes('Network error')) {
				toast.error('Network error: Please check your connection and try again');
			} else if (errorMessage.includes('Authentication')) {
				toast.error('Authentication error: Please log in again');
			} else {
				toast.error(`Failed to update status: ${errorMessage}`);
			}
		} finally {
			updating = false;
			selectedStatus = null;
			note = '';
		}
	}

	async function loadStatusHistory() {
		if (!glider?.name) return;

		loadingHistory = true;
		try {
			const logs = await getGliderStatusLogs(glider.name);
			// Сортуємо за createdAt desc і беремо останні 5
			statusHistory = logs
				.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
				.slice(0, 5);
		} catch (error) {
			console.error('Error loading status history:', error);
			statusHistory = [];
		} finally {
			loadingHistory = false;
		}
	}

	async function showTooltip() {
		tooltipVisible = true;
		await tick();
		const btnRect = buttonElement?.getBoundingClientRect();
		const tipRect = tooltipElement?.getBoundingClientRect();
		const aboveSpace = btnRect ? btnRect.top : 0;
		const belowSpace = btnRect ? window.innerHeight - btnRect.bottom : 0;
		const tipH = tipRect ? tipRect.height : 0;
		placeAbove = aboveSpace >= tipH + 12 || aboveSpace > belowSpace;
		if (statusHistory.length === 0) {
			await loadStatusHistory();
		}
	}

	function hideTooltip() {
		tooltipVisible = false;
	}

	function formatDate(dateString: string): string {
		try {
			const date = new Date(dateString);
			return date.toLocaleDateString('en-GB', {
				day: '2-digit',
				month: '2-digit',
				year: 'numeric',
				hour: '2-digit',
				minute: '2-digit'
			});
		} catch {
			return dateString;
		}
	}
</script>

{#if glider}
	<div class="status-dropdown-container relative">
		<button
			bind:this={buttonElement}
			class="status-button relative"
			class:updating
			class:disabled={updating}
			disabled={updating}
			on:click={toggleDropdown}
			on:mouseenter={showTooltip}
			on:mouseleave={hideTooltip}
			title={updating ? 'Updating status...' : 'Click to change status'}
		>
			<Badge
				color={badgeColor}
				class="status-badge flex cursor-pointer items-center gap-2 px-3 py-1 text-sm"
				style="background-color: {currentStatusColor || '#808080'}; color: {currentTextColor};"
			>
				{#if updating}
					<div
						class="spinner h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"
					></div>
				{/if}
				<span>{displayText}</span>
				{#if !updating}
					<ChevronDownOutline class="h-4 w-4" />
				{/if}
			</Badge>
		</button>

		{#if tooltipVisible && statusHistory.length > 0}
			<div
				bind:this={tooltipElement}
				class="tooltip absolute left-1/2 z-[1000] w-80 -translate-x-1/2 rounded-lg border bg-white p-3 shadow-lg"
				class:bottom-full={placeAbove}
				class:top-full={!placeAbove}
				class:mb-2={placeAbove}
				class:mt-2={!placeAbove}
			>
				<div class="mb-2 text-sm font-semibold">Last status change:</div>
				{#if loadingHistory}
					<div class="text-xs text-gray-500">Loading...</div>
				{:else if statusHistory.length > 0}
					{@const lastChange = statusHistory[0]}
					<div class="space-y-1 text-xs">
						<div>
							<strong>Change:</strong>
							{lastChange.oldGliderStatus?.name || 'Unknown'} → {lastChange.newGliderStatus?.name ||
								'Unknown'}
						</div>
						<div><strong>User:</strong> {lastChange.userEmail}</div>
						<div><strong>Date:</strong> {formatDate(lastChange.createdAt)}</div>
						<div><strong>Note:</strong> {lastChange.note}</div>
					</div>
				{:else}
					<div class="text-xs text-gray-500">No change history</div>
				{/if}
			</div>
		{/if}

		<!-- Dropdown -->
		{#if dropdownOpen}
			<div
				bind:this={dropdownElement}
				class="dropdown absolute left-0 top-full z-40 mt-1 w-48 rounded-lg border bg-white shadow-lg"
			>
				<div class="p-2">
					<div class="mb-2 text-sm font-semibold text-gray-700">Select status:</div>
					{#if loadingStatuses}
						<div class="px-3 py-2 text-sm text-gray-500">Loading...</div>
					{:else if availableStatuses.length === 0}
						<div class="px-3 py-2 text-sm text-gray-500">No available statuses</div>
					{:else}
						{#each availableStatuses as status}
							<button
								class="dropdown-item w-full rounded px-3 py-2 text-left text-sm hover:bg-gray-100"
								class:disabled={status.name === currentStatusName}
								disabled={status.name === currentStatusName}
								on:click={() => selectStatus(status)}
							>
								<div class="flex items-center gap-2">
									<div
										class="h-3 w-3 rounded-full"
										style="background-color: {getStatusColor(status)}"
									></div>
									{status.name}
									{#if status.name === currentStatusName}
										<span class="text-xs text-gray-500">(current)</span>
									{/if}
								</div>
							</button>
						{/each}
					{/if}
				</div>
			</div>
		{/if}
	</div>

	<!-- Confirmation Modal -->
	<Modal bind:open={showConfirmModal} size="md" autoclose={false}>
		<div class="p-6">
			<h3 class="mb-4 text-xl font-semibold text-gray-900">Confirm Status Change</h3>

			<div class="mb-4">
				<p class="text-gray-700">
					Are you sure you want to change the status of glider <strong>{glider?.name}</strong>
					from "<strong>{currentStatusName}</strong>" to "<strong
						>{selectedStatus?.name || ''}</strong
					>"?
				</p>
			</div>

			<div class="mb-6">
				<Label for="status-note" class="mb-2">Note (required) *</Label>
				<Textarea
					id="status-note"
					bind:value={note}
					placeholder="Enter reason for status change..."
					rows="3"
					required
				/>
			</div>

			<div class="flex justify-end space-x-3">
				<Button color="alternative" on:click={cancelStatusChange}>Cancel</Button>
				<Button color="primary" on:click={confirmStatusChange} disabled={!note.trim()}>
					Confirm
				</Button>
			</div>
		</div>
	</Modal>
{/if}

<style>
	.status-dropdown-container {
		display: inline-block;
	}

	.status-button {
		background: none;
		border: none;
		padding: 0;
		cursor: pointer;
	}

	.status-button:disabled {
		cursor: not-allowed;
		opacity: 0.6;
	}

	.dropdown {
		animation: fadeIn 0.15s ease-out;
	}

	.tooltip {
		animation: fadeIn 0.15s ease-out;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: translateY(-4px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.spinner {
		border-top-color: transparent;
	}

	.dropdown-item:disabled {
		opacity: 0.5;
		cursor: not-allowed;
	}

	.dropdown-item:disabled:hover {
		background-color: transparent;
	}
</style>
