<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import {
		startTimeTracking,
		stopTimeTracking,
		getLastShiftCached,
		type Shift
	} from '$lib/services/time-tracking.service';

	import { type UserData } from '$lib/services/users.service';
	import { trackingStore, setActiveRoute } from '$lib/stores/time-tracking.store';

	export let rideId: number;
	export let trackingRouteId: number;
	export let operatorId: string | number;
	export let operators: UserData[] = [];
	export let onError: (message: string) => void = () => {};
	export let onTrackingStateChange: (() => Promise<void>) | null = null;

	let isTracking = false;
	let isLoading = false;
	let isDisabled = false;
	let errorMessage = '';
	let successMessage = '';
	let trackingStartTime: Date | null = null;
	let elapsedTime = '00:00:00';
	let timer: number;
	let pilotEmail = '';
	let currentShiftId: number | null = null;

	$: storeState = $trackingStore;

	$: isDisabled = false;

	$: isDisabled = false;

	function getOperatorEmail(operatorId: string): string {
		const operator = operators.find((user) => user.id === operatorId);

		if (operator && operator.email) {
			return operator.email;
		} else {
			throw new Error(`No email found for operator ID: ${operatorId}`);
		}
	}

	function formatTimeForDisplay(timeString: string): string {
		const [hours, minutes, seconds] = timeString.split(':').map(Number);
		return `${hours}h ${minutes}m ${seconds}s`;
	}

	function startTimer() {
		if (timer) clearInterval(timer);
		timer = setInterval(updateElapsedTime, 1000);
	}

	function updateElapsedTime() {
		if (!trackingStartTime) return;

		const now = new Date();
		const diff = now.getTime() - trackingStartTime.getTime();

		const totalSeconds = Math.floor(diff / 1000);
		const hours = Math.floor(totalSeconds / 3600)
			.toString()
			.padStart(2, '0');
		const minutes = Math.floor((totalSeconds % 3600) / 60)
			.toString()
			.padStart(2, '0');
		const seconds = Math.floor(totalSeconds % 60)
			.toString()
			.padStart(2, '0');

		elapsedTime = `${hours}:${minutes}:${seconds}`;
	}

	function clearMessages() {
		errorMessage = '';
		successMessage = '';
	}

	async function handleStartTracking() {
		clearMessages();
		isLoading = true;

		if (operators.length === 0) {
			onError('Operators not loaded yet');
			isLoading = false;
			return;
		}

		if (!trackingRouteId) {
			onError('Tracking route ID is undefined for this ride!');
			isLoading = false;
			return;
		}

		let operatorEmail: string;
		try {
			operatorEmail = getOperatorEmail(operatorId);
		} catch (err) {
			onError('Operator is not a valid/authorized email');
			isLoading = false;
			return;
		}

		if (!operatorEmail) {
			onError('Operator is not a valid/authorized email');
			isLoading = false;
			return;
		}

		pilotEmail = operatorEmail;

		if ($trackingStore.hasActiveTracking && $trackingStore.activeRouteId !== trackingRouteId) {
			onError(`Active tracking on route #${$trackingStore.activeRouteId}. Please stop it first.`);
			isLoading = false;
			return;
		}

		try {
			const lastShift = await getLastShiftCached(operatorEmail);
			if (lastShift && !lastShift.stop_time) {
				if (lastShift.ride_id && lastShift.ride_id !== rideId) {
					onError(`Active tracking found for ride #${lastShift.ride_id}. Please stop it first.`);
					if (onTrackingStateChange) {
						await onTrackingStateChange();
					}
					isLoading = false;
					return;
				} else if (lastShift.ride_id === rideId) {
					isTracking = true;
					currentShiftId = lastShift.shift_id || lastShift.id;
					const startTimeStr = lastShift.start_time;
					trackingStartTime = new Date(startTimeStr + 'Z');
					updateElapsedTime();
					startTimer();
					setActiveRoute(trackingRouteId);
					if (onTrackingStateChange) {
						await onTrackingStateChange();
					}
					isLoading = false;
					return;
				}
			}
		} catch (error) {
			console.error('Error checking last shift before start:', error);
		}

		try {
			const description = `Track for Ride #${rideId}`;

			const trackingEvent = await startTimeTracking(
				trackingRouteId,
				pilotEmail,
				description,
				rideId
			);

			if (!trackingEvent || !trackingEvent.shift_id) {
				onError('Failed to start tracking - no response from server');
				isLoading = false;
				return;
			}

			currentShiftId = trackingEvent.shift_id;
			isTracking = true;
			setActiveRoute(trackingRouteId);
			trackingStartTime = new Date();
			updateElapsedTime();
			startTimer();
			successMessage = 'Time tracking started successfully';

			if (onTrackingStateChange) {
				await onTrackingStateChange();
			}
		} catch (error: any) {
			console.error('Error starting time tracking:', error);
			const errorMessage = error?.message || 'Failed to start time tracking';
			onError(errorMessage);

			if (
				errorMessage.includes('Active time tracking found') ||
				errorMessage.includes('active tracking')
			) {
				if (onTrackingStateChange) {
					await onTrackingStateChange();
				}
			}
		} finally {
			isLoading = false;
		}
	}

	async function handleStopTracking() {
		try {
			clearMessages();
			isLoading = true;

			if (!currentShiftId) {
				const lastShift = await getLastShiftCached(pilotEmail);
				if (lastShift && !lastShift.stop_time && lastShift.ride_id === rideId) {
					currentShiftId = lastShift.id || lastShift.shift_id;
				}
			}

			if (!currentShiftId) {
				throw new Error('No active shift found to stop');
			}

			const result = await stopTimeTracking(currentShiftId);

			if (!result) {
				throw new Error('Failed to stop tracking - no response from server');
			}

			isTracking = false;
			currentShiftId = null;
			setActiveRoute(null);
			if (timer) {
				clearInterval(timer);
				timer = undefined;
			}
			elapsedTime = '00:00:00';
			trackingStartTime = null;
			successMessage = 'Time tracking stopped successfully';

			if (onTrackingStateChange) {
				await onTrackingStateChange();
			}
		} catch (error: any) {
			console.error('Error stopping time tracking:', error);
			const errorMessage = error?.message || 'Failed to stop time tracking';
			onError(errorMessage);

			if (onTrackingStateChange) {
				await onTrackingStateChange();
			}
		} finally {
			isLoading = false;
		}
	}

	async function toggleTracking() {
		if (isLoading || isDisabled) return;

		if (isTracking) {
			await handleStopTracking();
		} else {
			await handleStartTracking();
		}
	}

	onMount(async () => {
		const checkWhenReady = () => {
			if (operators.length > 0) {
				checkOwnState();
			} else {
				setTimeout(checkWhenReady, 100);
			}
		};

		checkWhenReady();
	});

	async function checkOwnState() {
		try {
			const operatorEmail = getOperatorEmail(operatorId);
			const lastShift = await getLastShiftCached(operatorEmail);

			if (lastShift && !lastShift.stop_time && lastShift.ride_id === rideId) {
				isTracking = true;
				currentShiftId = lastShift.shift_id || lastShift.id;
				const startTimeStr = lastShift.start_time;
				trackingStartTime = new Date(startTimeStr + 'Z');
				updateElapsedTime();
				startTimer();
			} else {
				isTracking = false;
				currentShiftId = null;
				if (timer) clearInterval(timer);
				elapsedTime = '00:00:00';
			}
		} catch (error) {
			isTracking = false;
			currentShiftId = null;
			if (timer) clearInterval(timer);
			elapsedTime = '00:00:00';
		}
	}

	onDestroy(() => {
		if (timer) {
			clearInterval(timer);
		}
	});
</script>

<button
	class="tracking-button {isTracking ? 'stop' : 'start'} {isDisabled ? 'disabled' : ''}"
	on:click={toggleTracking}
	disabled={isLoading || isDisabled}
	title={isDisabled ? `Active tracking on route #${$trackingStore.activeRouteId}` : ''}
	data-testid={isTracking ? 'stop-tracking-btn' : 'ride-tracking-btn'}
>
	{#if isLoading}
		<div class="spinner"></div>
		<span>{isTracking ? 'Stopping...' : 'Starting...'}</span>
	{:else if isTracking}
		<svg class="icon" viewBox="0 0 24 24" width="16" height="16">
			<rect x="6" y="6" width="12" height="12" />
		</svg>
		<span>Stop</span>
		<div class="time-display" data-testid="tracking-timer">{formatTimeForDisplay(elapsedTime)}</div>
	{:else}
		<svg class="icon" viewBox="0 0 24 24" width="16" height="16">
			<polygon points="6,4 20,12 6,20" />
		</svg>
		<span>Start Tracking</span>
	{/if}
</button>

<style>
	.tracking-button {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 6px;
		padding: 8px 12px;
		border: none;
		border-radius: 6px;
		font-size: 13px;
		font-weight: 600;
		cursor: pointer;
		transition: all 0.2s ease;
		min-height: 36px;
		width: 140px; /* Static width */
	}

	.tracking-button.start {
		background-color: #16a34a;
		color: white;
	}

	.tracking-button.start:hover:not(:disabled) {
		background-color: #15803d;
	}

	.tracking-button.stop {
		background-color: #dc2626;
		color: white;
	}

	.tracking-button.stop:hover:not(:disabled) {
		background-color: #b91c1c;
	}

	.tracking-button.disabled {
		background-color: #9ca3af;
		cursor: not-allowed;
		opacity: 0.6;
	}

	.tracking-button:disabled {
		cursor: not-allowed;
		opacity: 0.7;
	}

	.icon {
		fill: currentColor;
		flex-shrink: 0;
	}

	.time-display {
		background-color: rgba(255, 255, 255, 0.2);
		padding: 2px 6px;
		border-radius: 3px;
		font-size: 11px;
		font-weight: 500;
		min-width: 35px;
		text-align: center;
	}

	.spinner {
		width: 14px;
		height: 14px;
		border: 2px solid rgba(255, 255, 255, 0.3);
		border-radius: 50%;
		border-top-color: white;
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}
</style>
