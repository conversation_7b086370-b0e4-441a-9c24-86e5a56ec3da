<script lang="ts">
	import { type UserData } from '$lib/services/users.service';
	import ShiftsHistoryModal from './ShiftsHistoryModal.svelte';

	export let operatorId: string;
	export let operators: UserData[] = []; // Pre-loaded operators

	let showModal = false;
	let pilotEmail = '';
	let loading = false;
	let error = '';

	function getOperatorEmail(operatorId: string): string {
		const operator = operators.find((user) => user.id === operatorId);

		if (operator && operator.email) {
			return operator.email;
		} else {
			throw new Error(`No email found for operator ID: ${operatorId}`);
		}
	}

	function openModal() {
		try {
			loading = true;
			error = '';

			if (!operatorId) {
				throw new Error('No operator ID provided');
			}

			if (operators.length === 0) {
				throw new Error('Operators not loaded yet');
			}

			pilotEmail = getOperatorEmail(operatorId);
			showModal = true;
		} catch (err) {
			console.error('Error opening history modal:', err);
			error = err instanceof Error ? err.message : 'Failed to open history modal';
		} finally {
			loading = false;
		}
	}

	function closeModal() {
		showModal = false;
		pilotEmail = '';
		error = '';
	}
</script>

<div class="history-button-container">
	{#if error}
		<div class="error-tooltip" title={error}>
			<button class="history-button error" disabled>
				<svg class="history-icon" viewBox="0 0 24 24" width="16" height="16">
					<path
						d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"
					/>
					<path d="M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67V7z" />
				</svg>
				<span>Error</span>
			</button>
		</div>
	{:else}
		<button
			class="history-button"
			on:click={openModal}
			disabled={loading}
			title="View time tracking history for operator {operatorId}"
			aria-label="View time tracking history for operator {operatorId}"
		>
			{#if loading}
				<div class="spinner"></div>
				<span>Loading...</span>
			{:else}
				<svg class="history-icon" viewBox="0 0 24 24" width="16" height="16">
					<path
						d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"
					/>
					<path d="M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67V7z" />
				</svg>
				<span>History</span>
			{/if}
		</button>
	{/if}
</div>

{#if showModal}
	<ShiftsHistoryModal bind:showModal {pilotEmail} onClose={closeModal} />
{/if}

<style>
	.history-button-container {
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.history-button {
		display: flex;
		align-items: center;
		gap: 6px;
		padding: 6px 12px;
		background-color: #f3f4f6;
		border: 1px solid #d1d5db;
		border-radius: 6px;
		color: #374151;
		font-size: 13px;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.2s ease;
		min-height: 32px;
	}

	.history-button:hover {
		background-color: #e5e7eb;
		border-color: #9ca3af;
		transform: translateY(-1px);
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	}

	.history-button:active {
		transform: translateY(0);
		box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
	}

	.history-button:focus {
		outline: none;
		box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
	}

	.history-button.error {
		background-color: #fef2f2;
		border-color: #fecaca;
		color: #dc2626;
		cursor: not-allowed;
	}

	.history-button:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	.error-tooltip {
		position: relative;
	}

	.spinner {
		width: 14px;
		height: 14px;
		border: 2px solid rgba(55, 65, 81, 0.3);
		border-radius: 50%;
		border-top-color: #374151;
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}

	.history-icon {
		fill: currentColor;
		flex-shrink: 0;
	}
</style>
