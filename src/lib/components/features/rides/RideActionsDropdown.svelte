<script lang="ts">
	import { onMount, createEventDispatcher } from 'svelte';
	import { goto } from '$app/navigation';
	import { DotsVerticalOutline } from 'flowbite-svelte-icons';
	import { Modal, Button } from 'flowbite-svelte';
	import type { UserData } from '$lib/services/users.service';
	import type { Glider } from '$lib/services/gliders.service';
	import { deleteRide } from '$lib/services/rides.service';
	import AssignToOperatorModal from './AssignToOperatorModal.svelte';
	import AssignToGliderModal from './AssignToGliderModal.svelte';
	import { formatDateTime } from '$lib/utils/datetime';

	export let rideId: number;
	export let operators: UserData[] = [];
	export let gliders: Glider[] = [];
	export let rideData: any = null; // For delete confirmation modal
	export let locations: any[] = []; // For location name lookup

	let dropdownOpen = false;
	let dropdownElement: HTMLDivElement;
	let buttonElement: HTMLButtonElement;
	let showAssignOperatorModal = false;
	let showAssignGliderModal = false;
	let showDeleteModal = false;
	let deleting = false;

	const dispatch = createEventDispatcher();

	onMount(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				dropdownElement &&
				!dropdownElement.contains(event.target as Node) &&
				buttonElement &&
				!buttonElement.contains(event.target as Node)
			) {
				dropdownOpen = false;
			}
		};

		document.addEventListener('click', handleClickOutside);

		return () => {
			document.removeEventListener('click', handleClickOutside);
		};
	});

	function toggleDropdown() {
		dropdownOpen = !dropdownOpen;
	}

	function handleAssignToOperator() {
		dropdownOpen = false;
		showAssignOperatorModal = true;
	}

	function handleAssignToGlider() {
		dropdownOpen = false;
		showAssignGliderModal = true;
	}

	function handleEditRide() {
		dropdownOpen = false;
		goto(`/rides/edit?id=${rideId}`);
	}

	function handleDeleteRide() {
		dropdownOpen = false;
		showDeleteModal = true;
	}

	function handleOperatorAssigned(event: CustomEvent) {
		dispatch('operatorAssigned', {
			rideId,
			operatorEmail: event.detail.operatorEmail,
			operatorId: event.detail.operatorId
		});
		showAssignOperatorModal = false;
	}

	function handleGliderAssigned(event: CustomEvent) {
		dispatch('gliderAssigned', {
			rideId,
			gliderName: event.detail.gliderName
		});
		showAssignGliderModal = false;
	}

	async function confirmDelete() {
		try {
			deleting = true;
			const success = await deleteRide(rideId);

			if (success) {
				dispatch('rideDeleted', { rideId });
				showDeleteModal = false;
			}
		} catch (error) {
			// Error deleting ride
		} finally {
			deleting = false;
		}
	}

	function cancelDelete() {
		showDeleteModal = false;
	}

	function getLocationName(locationId: number | string): string {
		if (!locationId || !locations.length) return String(locationId) || 'N/A';

		const location = locations.find((loc) => loc.id === Number(locationId));
		return location?.name || String(locationId) || 'N/A';
	}
</script>

<div class="actions-dropdown-container">
	<button
		bind:this={buttonElement}
		class="actions-button"
		on:click={toggleDropdown}
		title="More actions"
	>
		<DotsVerticalOutline class="h-4 w-4" />
	</button>

	{#if dropdownOpen}
		<div bind:this={dropdownElement} class="dropdown-menu">
			<button class="dropdown-item" on:click={handleAssignToOperator}> Assign to Operator </button>
			<button class="dropdown-item" on:click={handleAssignToGlider}> Assign to Glider </button>
			<button class="dropdown-item" on:click={handleEditRide}> Edit Ride </button>
			<button class="dropdown-item delete-item" on:click={handleDeleteRide}>
				Delete this Ride
			</button>
		</div>
	{/if}
</div>

<!-- Modals -->
{#if showAssignOperatorModal}
	<AssignToOperatorModal
		{rideId}
		{operators}
		on:assigned={handleOperatorAssigned}
		on:close={() => (showAssignOperatorModal = false)}
	/>
{/if}

{#if showAssignGliderModal}
	<AssignToGliderModal
		{rideId}
		{gliders}
		on:assigned={handleGliderAssigned}
		on:close={() => (showAssignGliderModal = false)}
	/>
{/if}

<!-- Delete Confirmation Modal -->
{#if showDeleteModal}
	<Modal bind:open={showDeleteModal} size="md" autoclose={false}>
		<div class="p-6">
			<h3 class="mb-4 text-xl font-semibold text-gray-900">Delete Ride</h3>

			<p class="mb-6 text-gray-700">Are you sure you want to delete this ride?</p>

			{#if rideData}
				<div class="mb-6 space-y-3 rounded-lg bg-gray-50 p-4">
					<div>
						<span class="font-semibold text-gray-800">Ride ID:</span>
						<span class="text-gray-700">{rideData.id}</span>
					</div>
					<div>
						<span class="font-semibold text-gray-800">Departure:</span>
						<span class="text-gray-700">{getLocationName(rideData.from_location)}</span>
					</div>
					<div>
						<span class="font-semibold text-gray-800">Arrival:</span>
						<span class="text-gray-700">{getLocationName(rideData.to_location)}</span>
					</div>
					<div>
						<span class="font-semibold text-gray-800">Departure Time:</span>
						<span class="text-gray-700">{formatDateTime(rideData.departure_time)}</span>
					</div>
				</div>
			{/if}

			<p class="mb-6 text-sm text-red-600">This action cannot be undone.</p>

			<div class="flex justify-end space-x-3">
				<Button color="alternative" on:click={cancelDelete} disabled={deleting}>Cancel</Button>
				<Button color="red" on:click={confirmDelete} disabled={deleting}>
					{#if deleting}
						<div
							class="mr-2 inline-block h-4 w-4 animate-spin rounded-full border-2 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"
						></div>
					{/if}
					{deleting ? 'Deleting...' : 'Delete'}
				</Button>
			</div>
		</div>
	</Modal>
{/if}

<style>
	.actions-dropdown-container {
		position: relative;
		display: inline-block;
		width: 36px; /* Small square button */
	}

	.actions-button {
		background: none;
		border: 1px solid #d1d5db;
		padding: 6px;
		cursor: pointer;
		transition: all 0.2s ease;
		width: 36px;
		height: 36px;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 6px;
		color: #6b7280;
	}

	.actions-button:hover {
		background-color: #f9fafb;
		border-color: #9ca3af;
	}

	.dropdown-menu {
		position: absolute;
		top: 100%;
		right: 0; /* Position to the right edge of the button */
		z-index: 50;
		min-width: 180px;
		margin-top: 4px;
		background: white;
		border: 1px solid #e5e7eb;
		border-radius: 8px;
		box-shadow:
			0 10px 15px -3px rgba(0, 0, 0, 0.1),
			0 4px 6px -2px rgba(0, 0, 0, 0.05);
	}

	.dropdown-item {
		display: flex;
		align-items: center;
		gap: 8px;
		width: 100%;
		padding: 12px 16px;
		text-align: left;
		background: none;
		border: none;
		cursor: pointer;
		transition: background-color 0.2s ease;
		font-size: 14px;
		color: #374151;
	}

	.dropdown-item:hover {
		background-color: #f9fafb;
	}

	.dropdown-item:first-child {
		border-top-left-radius: 8px;
		border-top-right-radius: 8px;
	}

	.dropdown-item:last-child {
		border-bottom-left-radius: 8px;
		border-bottom-right-radius: 8px;
	}

	.delete-item {
		color: #dc2626; /* Red text */
	}

	.delete-item:hover {
		background-color: #fef2f2; /* Light red background on hover */
		color: #b91c1c; /* Darker red on hover */
	}
</style>
