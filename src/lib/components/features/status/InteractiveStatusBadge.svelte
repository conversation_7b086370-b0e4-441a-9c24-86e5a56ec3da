<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { Button, Dropdown, DropdownItem, Badge, Tooltip } from 'flowbite-svelte';
	import { ChevronDownOutline, ClockOutline } from 'flowbite-svelte-icons';
	import type { <PERSON>lide<PERSON> } from '$lib/services/gliders.service';

	export let drone: Glider;
	export let availableStatuses: any[] = [];
	export let statusLogs: any[] = [];
	export let loading: boolean = false;

	const dispatch = createEventDispatcher();

	const STATUS_COLORS: { [key: string]: string } = {
		ready: '#4caf50',
		grounded: '#ff9800',
		unavailable: '#f44336',
		'maintenance due': '#9c27b0',
		'post-maint. checks': '#2196f3',
		'post-maintenance checks': '#2196f3',
		retired: '#607d8b'
	};

	$: currentStatus = drone?.gliderStatus;
	$: statusColor = getStatusColor(currentStatus);
	$: tooltipContent = getLastStatusChangeTooltip();

	let dropdownOpen = false;

	function getStatusColor(status: any): string {
		if (!status) return STATUS_COLORS.unavailable;

		if (status.colorHexcode) {
			return `#${status.colorHexcode}`;
		}

		const statusName = (status.name || status.toString()).toLowerCase();
		return STATUS_COLORS[statusName] || STATUS_COLORS.unavailable;
	}

	function getLastStatusChangeTooltip(): string {
		if (!statusLogs || statusLogs.length === 0) {
			return 'No status change history available';
		}

		const lastLog = statusLogs[0];
		if (!lastLog) return 'No status change history available';

		const oldStatus = lastLog.oldGliderStatus?.name || 'Unknown';
		const newStatus = lastLog.newGliderStatus?.name || 'Unknown';
		const userEmail = lastLog.userEmail || 'Unknown user';
		const date = new Date(lastLog.createdAt).toLocaleDateString('en-GB');
		const note = lastLog.note || 'No reason provided';

		return `Status changed from "${oldStatus}" to "${newStatus}" by ${userEmail} on ${date}\n\nReason: ${note}`;
	}

	function selectStatus(status: any) {
		if (loading || !drone) return;

		dispatch('statusSelected', {
			newStatus: status,
			currentStatus: currentStatus
		});

		dropdownOpen = false;
	}

	function viewStatusHistory() {
		dispatch('viewHistory');
	}

	function getStatusDisplayName(status: any): string {
		return status?.name || 'Unavailable';
	}

	function isCurrentStatus(status: any): boolean {
		return status?.id === currentStatus?.id;
	}
</script>

<div class="flex items-center gap-2">
	<div class="relative">
		<Button
			color="none"
			size="sm"
			class="flex min-w-[120px] items-center justify-center gap-2 rounded-full px-3 py-1 font-medium text-white transition-all duration-200 hover:opacity-90"
			style="background-color: {statusColor}"
			disabled={loading}
			title={tooltipContent}
		>
			<span class="text-sm">
				{getStatusDisplayName(currentStatus)}
			</span>
			{#if !loading}
				<ChevronDownOutline class="h-3 w-3" />
			{:else}
				<div
					class="h-3 w-3 animate-spin rounded-full border border-white border-t-transparent"
				></div>
			{/if}
		</Button>

		<Dropdown bind:open={dropdownOpen} class="w-56">
			<div class="p-2">
				<div class="mb-2 px-2 text-xs font-medium text-gray-500">Select Status:</div>
				{#each availableStatuses as status}
					<DropdownItem
						on:click={() => selectStatus(status)}
						class="flex items-center gap-3 px-2 py-2 {isCurrentStatus(status) ? 'bg-gray-100' : ''}"
					>
						<div
							class="h-3 w-3 flex-shrink-0 rounded-full"
							style="background-color: {getStatusColor(status)}"
						></div>
						<span class="text-sm {isCurrentStatus(status) ? 'font-medium' : ''}">
							{getStatusDisplayName(status)}
						</span>
						{#if isCurrentStatus(status)}
							<Badge color="green" size="xs" class="ml-auto">Current</Badge>
						{/if}
					</DropdownItem>
				{/each}
			</div>
		</Dropdown>
	</div>
	{#if statusLogs && statusLogs.length > 0}
		<Button
			color="light"
			size="sm"
			class="p-2"
			on:click={viewStatusHistory}
			title="View status history"
		>
			<ClockOutline class="h-4 w-4" />
		</Button>
	{/if}
</div>

<style>
	:global(.status-tooltip) {
		max-width: 300px;
		white-space: pre-line;
	}
</style>
