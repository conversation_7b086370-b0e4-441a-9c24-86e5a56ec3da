<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { Button, Dropdown, DropdownItem, Input, Badge } from 'flowbite-svelte';
	import { ChevronDownOutline, ExclamationCircleOutline } from 'flowbite-svelte-icons';
	import type { Glider } from '$lib/services/gliders.service';
	import type { Software } from '$lib/types/software.types';

	export let drone: Glider;
	export let componentType: 'jetson' | 'autopilot' | 'ftsPixhawk' | 'ftsRaspi';
	export let availableVersions: Software[] = [];
	export let loading: boolean = false;

	const dispatch = createEventDispatcher();
	const componentConfig = {
		jetson: {
			displayName: 'Jetson',
			currentField: 'jetsonSoftwareVersion',
			desiredField: 'desiredJetsonSoftwareVersion',
			updateField: 'desiredJetsonSoftwareVersionId',
			currentUpdateField: 'jetsonSoftwareVersionId',
			typeId: 2
		},
		autopilot: {
			displayName: 'Autopilot',
			currentField: 'autopilotSoftwareVersion',
			desiredField: 'desiredAutopilotSoftwareVersion',
			updateField: 'desiredAutopilotSoftwareVersionId',
			currentUpdateField: 'autopilotSoftwareVersionId',
			typeId: 1
		},
		ftsPixhawk: {
			displayName: 'FTS Pixhawk',
			currentField: 'ftsPixhawkSoftwareVersion',
			desiredField: 'desiredFtsPixhawkSoftwareVersion',
			updateField: 'desiredFtsPixhawkSoftwareVersionId',
			currentUpdateField: 'ftsPixhawkSoftwareVersionId',
			typeId: 5
		},
		ftsRaspi: {
			displayName: 'FTS server',
			currentField: 'ftsRaspiSoftwareVersion',
			desiredField: 'desiredFtsRaspiSoftwareVersion',
			updateField: 'desiredFtsRaspiSoftwareVersionId',
			currentUpdateField: 'ftsRaspiSoftwareVersionId',
			typeId: 4
		}
	};

	$: config = componentConfig[componentType];
	$: currentVersion = drone?.[config.currentField];
	$: desiredVersion = drone?.[config.desiredField];
	$: hasVersionMismatch = isVersionMismatch(currentVersion, desiredVersion);

	let searchTerm = '';
	let filteredVersions: Software[] = [];
	let dropdownOpen = false;
	let dropdownOpenCurrent = false;
	import { compareVersionNamesDesc } from '$lib/utils/version';

	$: {
		const sorted = [...availableVersions].sort((a, b) => compareVersionNamesDesc(a.name, b.name));
		if (searchTerm.trim()) {
			filteredVersions = sorted.filter((version) =>
				version.name.toLowerCase().includes(searchTerm.toLowerCase())
			);
		} else {
			filteredVersions = sorted;
		}
	}

	function isVersionMismatch(current: any, desired: any): boolean {
		if (!current || !desired) return false;
		return current.id !== desired.id;
	}

	function selectVersion(version: Software) {
		dispatch('versionSelected', {
			componentType,
			version,
			oldVersion: desiredVersion?.name || 'none',
			newVersion: version.name,
			updateField: config.updateField
		});
		dropdownOpen = false;
		searchTerm = '';
	}

	function selectCurrentVersion(version: Software) {
		dispatch('versionSelected', {
			componentType,
			version,
			oldVersion: currentVersion?.name || 'none',
			newVersion: version.name,
			updateField: config.currentUpdateField
		});
		dropdownOpenCurrent = false;
		searchTerm = '';
	}

	function handleSearchInput(event: Event) {
		const target = event.target as HTMLInputElement;
		searchTerm = target.value;
	}
</script>

<tr class="border-b">
	<td class="px-4 py-3 font-medium text-gray-900">{config.displayName}</td>
	<td class="px-4 py-3">
		<div class="relative">
			<Button color="light" size="sm" class="flex items-center gap-2" disabled={loading}>
				<span class="font-mono text-sm">{currentVersion?.name || 'Select version'}</span>
				<ChevronDownOutline class="h-4 w-4" />
			</Button>
			<Dropdown bind:open={dropdownOpenCurrent} class="max-h-96 w-80 overflow-y-auto">
				<div class="border-b p-2">
					<Input
						placeholder="Search versions..."
						size="sm"
						bind:value={searchTerm}
						on:input={handleSearchInput}
					/>
				</div>
				<div class="max-h-60 overflow-y-auto">
					{#each filteredVersions as version}
						<DropdownItem
							on:click={() => selectCurrentVersion(version)}
							class="flex items-center justify-between"
						>
							<span class="font-mono">{version.name}</span>
							{#if version.id === currentVersion?.id}
								<Badge color="green" size="xs">Selected</Badge>
							{/if}
						</DropdownItem>
					{/each}
					{#if filteredVersions.length === 0}
						<div class="px-4 py-2 text-sm text-gray-500">No versions found</div>
					{/if}
				</div>
			</Dropdown>
		</div>
	</td>
	<td class="px-4 py-3">
		<div class="relative">
			<Button
				color="light"
				size="sm"
				class="flex items-center gap-2 {hasVersionMismatch ? 'border-yellow-400 bg-yellow-50' : ''}"
				disabled={loading}
			>
				<span class="font-mono text-sm">
					{desiredVersion?.name || 'Select version'}
				</span>
				{#if hasVersionMismatch}
					<ExclamationCircleOutline class="h-4 w-4 text-yellow-500" />
				{/if}
				<ChevronDownOutline class="h-4 w-4" />
			</Button>

			<Dropdown bind:open={dropdownOpen} class="max-h-96 w-80 overflow-y-auto">
				<div class="border-b p-2">
					<Input
						placeholder="Search versions..."
						size="sm"
						bind:value={searchTerm}
						on:input={handleSearchInput}
					/>
				</div>
				<div class="max-h-60 overflow-y-auto">
					{#each filteredVersions as version}
						<DropdownItem
							on:click={() => selectVersion(version)}
							class="flex items-center justify-between"
						>
							<span class="font-mono">{version.name}</span>
							{#if version.id === desiredVersion?.id}
								<Badge color="green" size="xs">Selected</Badge>
							{/if}
						</DropdownItem>
					{/each}

					{#if filteredVersions.length === 0}
						<div class="px-4 py-2 text-sm text-gray-500">No versions found</div>
					{/if}
				</div>
			</Dropdown>
		</div>
	</td>
	<td class="px-4 py-3">
		{#if hasVersionMismatch}
			<Badge color="yellow" size="xs" class="flex items-center gap-1">
				<ExclamationCircleOutline class="h-3 w-3" />
				Mismatch
			</Badge>
		{:else}
			<Badge color="gray" size="xs">None</Badge>
		{/if}
	</td>
</tr>
