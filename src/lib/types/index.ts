export interface Glider {
	id: number;
	line: string;
	generation: string;
	number: string;
	name: string;
	pixhawkUuid: string;
	gliderMode: any;
	gliderStatus: any;
	autopilotSoftwareVersion: any;
	desiredAutopilotSoftwareVersion: any;
	jetsonSoftwareVersion: any;
	desiredJetsonSoftwareVersion: any;
	ftsPixhawkSoftwareVersion: any;
	desiredFtsPixhawkSoftwareVersion: any;
	ftsRaspiSoftwareVersion: any;
	desiredFtsRaspiSoftwareVersion: any;
	company: any;
	region: any;
	vpnIp: string;
	vpnNetworkId: string;
	manufacturingDate: string;
	registrationCode: string;
	registrationComplete: boolean;
	inUse: boolean;
	designDeviation: string;
	designComplianceRecord: string;
	totalFlightTimeInSeconds: number;
	totalFlightTimeSinceLastMaintenanceInSeconds: number;
	createdAt: string;
	updatedAt: string;
	flightPreConditionRead: any;
}

export interface Location {
	id: number;
	name: string;
	pictureUrl: string;
	videoUrl: string;
	latitude: number | null;
	longitude: number | null;
	radius: number | null;
	locationCategory: {
		id: number;
		name: string;
		description: string;
		createdAt: string;
		updatedAt: string;
	} | null;
	locationStatus: {
		id: number;
		name: string;
		description: string;
		createdAt: string;
		updatedAt: string;
	} | null;
	createdAt: string;
	updatedAt: string;
}

export interface User {
	id: string;
	email: string;
	firstName: string;
	lastName: string;
	role: string;
	status: string;
	created_at: string;
	updated_at: string;
}

export interface Route {
	start_location_id: number;
	start_location_name: string;
	end_location_id: number;
	end_location_name: string;
	emergency_contact: string;
	known_dangers: string;
	extra_notes: string;
	external_route_id: string;
	customer_id: number;
	id: number;
	customer: {
		name: string;
		id: number;
	};
}

export interface RideStatus {
	id: number;
	name: string;
	description: string;
}

export interface CancelReason {
	name: string;
	description: string;
	id: number;
}

export interface Ride {
	id?: number;
	from_location: number;
	to_location: number;
	departure_time: string;
	arrival_time?: string | null;
	ride_status_id: number;
	glider_id: number;
	glider_name?: string;
	operator_id: string;
	has_package: boolean;
	package_description?: string;
	route_id?: number | null;
	cancel_reason_id?: number | null;
	route?: Route;
	ride_status?: RideStatus;
	cancel_reason?: CancelReason | null;
}
