export interface UnscheduledMaintenanceRequest {
	glider_name: string;
	user_email: string;
	maintenance_reasons: string;
	extra_notes: string;
}

export interface RequestMaintenanceDialogData {
	gliderName: string;
	userEmail: string;
}

export interface MaintenanceRequestResponse {
	message: string;
	success?: boolean;
}

export const MAINTENANCE_REASONS = ['Incident', 'Software Update', 'Other'] as const;

export type MaintenanceReason = (typeof MAINTENANCE_REASONS)[number];
