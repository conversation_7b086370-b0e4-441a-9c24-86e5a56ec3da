export type OperatorLite = {
	id: string;
	email: string;
	firstName?: string;
	lastName?: string;
	username?: string;
};

type PersistedData = {
	list: OperatorLite[];
	loadedAt: number;
	ttlMs: number;
};

const STORAGE_KEY = 'operatorsCache:v1';

let map = new Map<string, string>();
let lastLoadedAt = 0;
let ttl = 24 * 60 * 60 * 1000;
let inflight: Promise<void> | null = null;

function now() {
	return Date.now();
}

function isBrowser() {
	return typeof window !== 'undefined' && typeof localStorage !== 'undefined';
}

function loadFromStorage() {
	if (!isBrowser()) return;
	const raw = localStorage.getItem(STORAGE_KEY);
	if (!raw) return;
	try {
		const data = JSON.parse(raw) as PersistedData;
		if (data && Array.isArray(data.list) && now() - data.loadedAt < data.ttlMs) {
			map = new Map(data.list.map((o) => [String(o.id), o.email || '']));
			lastLoadedAt = data.loadedAt;
			ttl = data.ttlMs || ttl;
		}
	} catch {}
}

function saveToStorage(list: OperatorLite[]) {
	if (!isBrowser()) return;
	const payload: PersistedData = { list, loadedAt: now(), ttlMs: ttl };
	try {
		localStorage.setItem(STORAGE_KEY, JSON.stringify(payload));
	} catch {}
}

export function initOperatorsCache(initial?: OperatorLite[], ttlMs?: number): void {
	if (ttlMs && ttlMs > 0) ttl = ttlMs;
	loadFromStorage();
	if (initial && initial.length > 0) setOperators(initial);
}

export function setOperators(list: OperatorLite[]): void {
	map = new Map(list.map((o) => [String(o.id), o.email || '']));
	lastLoadedAt = now();
	saveToStorage(list);
}

export function getOperatorEmailById(id: string | number): string | undefined {
	if (!id) return undefined;
	const k = String(id);
	return map.get(k);
}

export async function ensureOperatorsLoaded(fetchFn: () => Promise<OperatorLite[]>): Promise<void> {
	if (map.size > 0 && now() - lastLoadedAt < ttl) return;
	if (inflight) return inflight;
	inflight = (async () => {
		loadFromStorage();
		if (map.size > 0 && now() - lastLoadedAt < ttl) return;
		const list = await fetchFn();
		setOperators(list || []);
	})();
	try {
		await inflight;
	} finally {
		inflight = null;
	}
}
