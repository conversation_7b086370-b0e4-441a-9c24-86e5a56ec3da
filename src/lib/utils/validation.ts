import type { Glider, Location, User } from '$lib/types';
import { findPilotByEmailOrName } from '$lib/services/users.service';

function excelDateToJSDate(excelDate: number): Date | null {
	const millisecondsPerDay = 24 * 60 * 60 * 1000;
	return new Date((excelDate - 25569) * millisecondsPerDay);
}

export interface ValidationError {
	field: string;
	message: string;
}

export interface ValidationResult {
	isValid: boolean;
	errors: ValidationError[];
	missingFields: string[];
}

export interface RowValidationContext {
	gliders: Glider[];
	locations: Location[];
	pilots: User[];
	glidersMapByName: Map<string, Glider>;
	glidersMapById: Map<string, Glider>;
	locationsMapByName: Map<string, Location>;
	locationsMapById: Map<string, Location>;
	pilotsMapById: Map<string, User>;
}

export interface ParsedRow {
	glider_name?: string;
	glider_id?: string;
	start_location_name?: string;
	start_location_id?: string;
	end_location_name?: string;
	end_location_id?: string;
	pilot_email?: string;
	pilot_name?: string;
	pilot_id?: string;
	date?: any;
	scheduled_start_time?: any;
	scheduled_end_time?: any;
	notes?: string;
	description?: string;
	logs?: string;
}

export interface ValidatedRow extends ParsedRow {
	_validationErrors: string[];
	_missingFields: string[];
}

export function validateScheduleRow(row: ParsedRow, context: RowValidationContext): ValidatedRow {
	const validatedRow: ValidatedRow = { ...row, _validationErrors: [], _missingFields: [] };
	const glider =
		context.glidersMapByName.get(row.glider_name) || context.glidersMapById.get(row.glider_id);
	if (!glider) {
		validatedRow._validationErrors.push('Missing or invalid glider');
		validatedRow._missingFields.push('glider');
	} else {
		validatedRow.glider_id = String(glider.id);
	}
	const startLocation =
		context.locationsMapByName.get(row.start_location_name) ||
		context.locationsMapById.get(row.start_location_id);
	if (!startLocation) {
		validatedRow._validationErrors.push('Missing or invalid start location');
		validatedRow._missingFields.push('start_location');
	} else {
		validatedRow.start_location_id = String(startLocation.id);
	}
	const endLocation =
		context.locationsMapByName.get(row.end_location_name) ||
		context.locationsMapById.get(row.end_location_id);
	if (!endLocation) {
		validatedRow._validationErrors.push('Missing or invalid end location');
		validatedRow._missingFields.push('end_location');
	} else {
		validatedRow.end_location_id = String(endLocation.id);
	}

	// Validate pilot
	let pilot = null;
	if (row.pilot_email) {
		pilot = findPilotByEmailOrName(context.pilots, row.pilot_email);
	}
	if (!pilot && row.pilot_name) {
		pilot = findPilotByEmailOrName(context.pilots, row.pilot_name);
	}
	if (!pilot && row.pilot_id && !row.pilot_id.includes('@')) {
		pilot = context.pilotsMapById.get(row.pilot_id);
	}

	if (pilot) {
		validatedRow.pilot_id = String(pilot.id);
	} else if (row.pilot_email || row.pilot_name || row.pilot_id) {
		const pilotInfo = row.pilot_email || row.pilot_name || row.pilot_id;
		validatedRow._validationErrors.push(
			`Pilot not found: ${pilotInfo}. Please check if the email exists in the system.`
		);
		validatedRow._missingFields.push('pilot');
	} else {
		validatedRow._validationErrors.push('Missing pilot information');
		validatedRow._missingFields.push('pilot');
	}
	try {
		const dateTimeValidation = validateDateTime(row);
		if (!dateTimeValidation.isValid) {
			validatedRow._validationErrors.push(...dateTimeValidation.errors.map((e) => e.message));
			validatedRow._missingFields.push(...dateTimeValidation.missingFields);
		} else {
			if (dateTimeValidation.scheduledStartTime) {
				validatedRow.scheduled_start_time = dateTimeValidation.scheduledStartTime;
				if (!validatedRow.date) {
					validatedRow.date = dateTimeValidation.scheduledStartTime;
				}
			}
			if (dateTimeValidation.scheduledEndTime) {
				validatedRow.scheduled_end_time = dateTimeValidation.scheduledEndTime;
			}
		}
	} catch (e) {
		validatedRow._validationErrors.push(`Data processing error: ${e.message}`);
	}

	if (!validatedRow.notes) {
		validatedRow.notes = validatedRow.description || validatedRow.logs || '';
	}

	return validatedRow;
}

interface DateTimeValidationResult {
	isValid: boolean;
	errors: ValidationError[];
	missingFields: string[];
	scheduledStartTime?: Date;
	scheduledEndTime?: Date;
}

export function validateDateTime(row: ParsedRow): DateTimeValidationResult {
	const result: DateTimeValidationResult = {
		isValid: true,
		errors: [],
		missingFields: []
	};

	try {
		let processedDate = null;
		let processedTime = null;

		if (typeof row.date === 'number') {
			processedDate = excelDateToJSDate(row.date);
		} else if (row.date instanceof Date) {
			processedDate = row.date;
		} else if (row.date && typeof row.date === 'string') {
			const parsedDate = new Date(row.date);
			if (!isNaN(parsedDate.getTime())) {
				processedDate = parsedDate;
			}
		}

		if (row.scheduled_start_time instanceof Date) {
			processedTime = row.scheduled_start_time;
		} else if (typeof row.scheduled_start_time === 'number') {
			if (processedDate) {
				const hours = Math.floor(row.scheduled_start_time * 24);
				const minutes = Math.floor((row.scheduled_start_time * 24 * 60) % 60);
				processedTime = new Date(processedDate);
				processedTime.setHours(hours, minutes, 0, 0);
			}
		} else if (typeof row.scheduled_start_time === 'string') {
			if (processedDate) {
				const timeMatch = row.scheduled_start_time.match(/(\d{1,2}):(\d{2})/);
				if (timeMatch) {
					processedTime = new Date(processedDate);
					processedTime.setHours(parseInt(timeMatch[1]), parseInt(timeMatch[2]), 0, 0);
				}
			} else {
				// Try to parse as full datetime string
				const parsedDateTime = new Date(row.scheduled_start_time);
				if (!isNaN(parsedDateTime.getTime())) {
					processedTime = parsedDateTime;
				}
			}
		}

		if (processedTime && !isNaN(processedTime.getTime())) {
			result.scheduledStartTime = processedTime;
		} else {
			result.isValid = false;
			result.errors.push({ field: 'start_time', message: 'Invalid date or time format' });
			result.missingFields.push('start_time', 'date');
		}

		if (row.scheduled_end_time) {
			let processedEndTime = null;

			if (row.scheduled_end_time instanceof Date) {
				processedEndTime = row.scheduled_end_time;
			} else if (typeof row.scheduled_end_time === 'number') {
				if (processedDate) {
					const hours = Math.floor(row.scheduled_end_time * 24);
					const minutes = Math.floor((row.scheduled_end_time * 24 * 60) % 60);
					processedEndTime = new Date(processedDate);
					processedEndTime.setHours(hours, minutes, 0, 0);
				}
			} else if (typeof row.scheduled_end_time === 'string') {
				if (processedDate) {
					const timeMatch = row.scheduled_end_time.match(/(\d{1,2}):(\d{2})/);
					if (timeMatch) {
						processedEndTime = new Date(processedDate);
						processedEndTime.setHours(parseInt(timeMatch[1]), parseInt(timeMatch[2]), 0, 0);
					}
				} else {
					const parsedDateTime = new Date(row.scheduled_end_time);
					if (!isNaN(parsedDateTime.getTime())) {
						processedEndTime = parsedDateTime;
					}
				}
			}

			if (processedEndTime && !isNaN(processedEndTime.getTime())) {
				result.scheduledEndTime = processedEndTime;
			} else {
				result.errors.push({ field: 'end_time', message: 'Invalid end time format' });
				result.missingFields.push('end_time');
			}
		}
	} catch (e) {
		result.isValid = false;
		result.errors.push({ field: 'datetime', message: `Date/time processing error: ${e.message}` });
	}

	return result;
}

export function hasValidationErrors(row: ValidatedRow): boolean {
	return row._validationErrors && row._validationErrors.length > 0;
}

export function hasFieldError(row: ValidatedRow, fieldName: string): boolean {
	return row._missingFields && row._missingFields.includes(fieldName);
}

export function getRowsWithErrors(rows: ValidatedRow[]): ValidatedRow[] {
	return rows.filter((row) => hasValidationErrors(row));
}

export function getMissingFieldsSummary(rowsWithErrors: ValidatedRow[]): string[] {
	const missingFieldsSet = new Set<string>();
	rowsWithErrors.forEach((row) => {
		if (row._missingFields) {
			row._missingFields.forEach((field) => missingFieldsSet.add(field));
		}
	});
	return Array.from(missingFieldsSet);
}
