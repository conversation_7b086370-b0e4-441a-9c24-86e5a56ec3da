import { browser } from '$app/environment';
export function ensureUTC(dateTimeString: string): string {
	if (!dateTimeString) return dateTimeString;
	const hasTimezone =
		dateTimeString.includes('Z') ||
		dateTimeString.includes('+') ||
		dateTimeString.includes('-', 10);
	if (!hasTimezone) {
		return dateTimeString + 'Z';
	}
	return dateTimeString;
}

export function getUserTimezone(): string {
	if (!browser) {
		return 'UTC';
	}
	try {
		const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
		if (!timezone || timezone.trim() === '') {
			return 'UTC';
		}
		return timezone;
	} catch {
		return 'UTC';
	}
}

export function getTimezoneDetectionStatus(): {
	timezone: string;
	isDetected: boolean;
	error?: string;
} {
	if (!browser) {
		return { timezone: 'UTC', isDetected: false, error: 'Not in browser environment' };
	}
	try {
		const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
		if (!timezone || timezone.trim() === '') {
			return { timezone: 'UTC', isDetected: false, error: 'Empty timezone returned' };
		}
		return { timezone, isDetected: true };
	} catch (error: any) {
		return { timezone: 'UTC', isDetected: false, error: error.message };
	}
}

export function parseDateDMY(value: string): Date | null {
	if (!value) return null;
	const s = value.trim();
	const iso = s.match(/^([0-9]{4})-([0-9]{2})-([0-9]{2})$/);
	if (iso) {
		const y = parseInt(iso[1]);
		const m = parseInt(iso[2]);
		const d = parseInt(iso[3]);
		return new Date(y, m - 1, d);
	}
	const dmy = s.match(/^([0-9]{1,2})[./-]([0-9]{1,2})[./-]([0-9]{2,4})$/);
	if (dmy) {
		const d = parseInt(dmy[1]);
		const m = parseInt(dmy[2]);
		let y = parseInt(dmy[3]);
		if (y < 100) y += 2000;
		return new Date(y, m - 1, d);
	}
	const ymd = s.match(/^([0-9]{4})[./-]([0-9]{1,2})[./-]([0-9]{1,2})$/);
	if (ymd) {
		const y = parseInt(ymd[1]);
		const m = parseInt(ymd[2]);
		const d = parseInt(ymd[3]);
		return new Date(y, m - 1, d);
	}
	return null;
}

export function parseDateTimeDMY(value: string): Date | null {
	if (!value) return null;
	const s = value.trim();
	const dt = s.match(
		/^([0-9]{1,2})[./-]([0-9]{1,2})[./-]([0-9]{2,4})(?:\s+([0-9]{1,2}):([0-9]{2}))?$/
	);
	if (dt) {
		const d = parseInt(dt[1]);
		const m = parseInt(dt[2]);
		let y = parseInt(dt[3]);
		if (y < 100) y += 2000;
		const hh = dt[4] ? parseInt(dt[4]) : 0;
		const mm = dt[5] ? parseInt(dt[5]) : 0;
		return new Date(y, m - 1, d, hh, mm, 0, 0);
	}
	const iso = s.match(/^([0-9]{4})-([0-9]{2})-([0-9]{2})[T\s]([0-9]{1,2}):([0-9]{2})$/);
	if (iso) {
		const y = parseInt(iso[1]);
		const m = parseInt(iso[2]);
		const d = parseInt(iso[3]);
		const hh = parseInt(iso[4]);
		const mm = parseInt(iso[5]);
		return new Date(y, m - 1, d, hh, mm, 0, 0);
	}
	return null;
}

export function formatDate(dateTimeString: string, timezone?: string): string {
	if (!dateTimeString) return '-';
	try {
		const date = new Date(ensureUTC(dateTimeString));
		return new Intl.DateTimeFormat('de-DE', {
			timeZone: timezone || getUserTimezone(),
			year: 'numeric',
			month: '2-digit',
			day: '2-digit'
		}).format(date);
	} catch {
		return '-';
	}
}

export function formatTime(dateTimeString: string, timezone?: string): string {
	if (!dateTimeString) return '-';
	try {
		const date = new Date(ensureUTC(dateTimeString));
		return new Intl.DateTimeFormat('de-DE', {
			timeZone: timezone || getUserTimezone(),
			hour: '2-digit',
			minute: '2-digit',
			hour12: false
		}).format(date);
	} catch {
		return '-';
	}
}
export function formatDateTime(dateTimeString: string, timezone?: string): string {
	if (!dateTimeString) return 'N/A';
	try {
		const date = new Date(ensureUTC(dateTimeString));
		return new Intl.DateTimeFormat('de-DE', {
			timeZone: timezone || getUserTimezone(),
			year: 'numeric',
			month: '2-digit',
			day: '2-digit',
			hour: '2-digit',
			minute: '2-digit',
			second: '2-digit',
			hour12: false
		}).format(date);
	} catch {
		return 'N/A';
	}
}

export function formatDateTimeDetailed(dateTimeString: string, timezone?: string): string {
	if (!dateTimeString) return 'N/A';
	try {
		const date = new Date(ensureUTC(dateTimeString));
		return new Intl.DateTimeFormat('de-DE', {
			timeZone: timezone || getUserTimezone(),
			weekday: 'short',
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit',
			hour12: false
		}).format(date);
	} catch {
		return 'N/A';
	}
}

export function formatTimeUTC(dateTimeString: string): string {
	if (!dateTimeString) return '-';
	try {
		const utcDate = new Date(ensureUTC(dateTimeString));
		return new Intl.DateTimeFormat('de-DE', {
			timeZone: 'UTC',
			hour: '2-digit',
			minute: '2-digit',
			hour12: false
		}).format(utcDate);
	} catch {
		return '-';
	}
}

export function formatDateUTC(dateTimeString: string): string {
	if (!dateTimeString) return '-';
	try {
		const utcDate = new Date(ensureUTC(dateTimeString));
		return new Intl.DateTimeFormat('de-DE', {
			timeZone: 'UTC',
			year: 'numeric',
			month: '2-digit',
			day: '2-digit'
		}).format(utcDate);
	} catch {
		return '-';
	}
}

export function getTimezoneDisplayName(timezone: string): string {
	try {
		if (!timezone || timezone === 'UTC') {
			return 'UTC';
		}
		const now = new Date();
		const formatter = new Intl.DateTimeFormat('en', { timeZone: timezone, timeZoneName: 'short' });
		const parts = formatter.formatToParts(now);
		const timeZoneName = parts.find((part) => part.type === 'timeZoneName')?.value;
		return timeZoneName || timezone;
	} catch {
		return timezone;
	}
}

export function formatFlightTime(seconds: number | null | undefined): string {
	if (seconds == null || seconds < 0) return 'No data';

	const hours = Math.floor(seconds / 3600);
	const minutes = Math.floor((seconds % 3600) / 60);
	const remainingSeconds = seconds % 60;

	if (hours > 0) {
		return `${hours}h ${minutes}m ${remainingSeconds}s`;
	} else if (minutes > 0) {
		return `${minutes}m ${remainingSeconds}s`;
	} else {
		return `${remainingSeconds}s`;
	}
}
