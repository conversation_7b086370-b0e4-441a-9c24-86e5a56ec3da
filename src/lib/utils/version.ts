export interface ParsedVersion {
	major: number;
	minor: number;
	patch: number;
	rc: number | null;
}

export function parseVersion(name: string): ParsedVersion | null {
	const m = name.trim().match(/^(\d+)\.(\d+)\.(\d+)(?:-rc(\d+))?$/i);
	if (!m) return null;
	return {
		major: parseInt(m[1], 10),
		minor: parseInt(m[2], 10),
		patch: parseInt(m[3], 10),
		rc: m[4] ? parseInt(m[4], 10) : null
	};
}

export function compareVersionNamesDesc(aName: string, bName: string): number {
	const a = parseVersion(aName);
	const b = parseVersion(bName);
	if (a && b) {
		if (a.major !== b.major) return b.major - a.major;
		if (a.minor !== b.minor) return b.minor - a.minor;
		if (a.patch !== b.patch) return b.patch - a.patch;
		if (a.rc === null && b.rc !== null) return -1;
		if (a.rc !== null && b.rc === null) return 1;
		if (a.rc === null && b.rc === null) return 0;
		return (b.rc as number) - (a.rc as number);
	}
	return bName.localeCompare(aName);
}
