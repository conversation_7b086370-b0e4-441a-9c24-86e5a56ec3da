export const VALIDATION_MESSAGES = {
	MISSING_GLIDER: 'Missing or invalid glider',
	MISSING_START_LOCATION: 'Missing or invalid start location',
	MISSING_END_LOCATION: 'Missing or invalid end location',
	MISSING_PILOT: 'Missing pilot information',
	PILOT_NOT_FOUND: (pilotInfo: string) =>
		`Pilot not found: ${pilotInfo}. Please check if the email exists in the system.`,
	INVALID_DATE_TIME: 'Invalid date or time format',
	INVALID_END_TIME: 'Invalid end time format',
	MISSING_END_TIME: 'Missing end time',
	DATA_PROCESSING_ERROR: (error: string) => `Data processing error: ${error}`,
	DATE_TIME_PROCESSING_ERROR: (error: string) => `Date/time processing error: ${error}`
};

export const FIELD_NAMES = {
	glider: 'Glider',
	start_location: 'Start Location',
	end_location: 'End Location',
	start_time: 'Start Time',
	end_time: 'End Time',
	pilot: 'Pilot',
	date: 'Date'
};

export const UPLOAD_MESSAGES = {
	CANNOT_UPLOAD_MISSING_FIELDS: (missingFields: string) =>
		`Cannot upload: Missing required fields: ${missingFields}. Please fix the highlighted fields and try again.`,
	CANNOT_UPLOAD_VALIDATION_ERRORS:
		'Cannot upload: Some selected rows have validation errors. Please fix them first.',
	SELECT_ROWS_TO_UPLOAD: 'Select rows to upload',
	SELECTED_ROWS: (selected: number, total: number) => `Selected ${selected} of ${total} rows`,
	UPLOAD_SUCCESS: (current: number, total: number) => `Uploaded flight ${current}/${total}`,
	UPLOAD_ERROR: (current: number, total: number, error: string) =>
		`Error processing flight ${current}/${total}: ${error}`,
	NETWORK_ERROR:
		'Network error: cannot connect to server. Check your internet connection or server availability.',
	SERVER_ERROR: (status: number, message: string) => `Server error (${status}): ${message}`,
	VALIDATION_ERROR: (error: string) => `Validation error: ${error}`,
	CANNOT_VALIDATE_NO_DATA: 'Cannot validate rows: reference data not available',
	ERROR_PARSING_EXCEL: 'Error parsing Excel sheet'
};

export const TIMEZONE_MESSAGES = {
	TIMEZONE_INFO: (timezone: string, displayName: string) =>
		`Your current timezone is detected as ${timezone} (${displayName}). All times in your Excel file should be in this timezone. They will be automatically converted to UTC when uploaded to the server.`,
	TIMEZONE_DETECTION_FAILED:
		'Unable to detect your timezone. Using UTC as default. Please ensure your times are in the correct timezone.',
	TIMEZONE_FALLBACK: 'Timezone detection failed, using UTC as fallback.'
};

export const ARIA_LABELS = {
	GLIDER_SELECT: 'Select glider for this flight',
	START_LOCATION_SELECT: 'Select start location for this flight',
	END_LOCATION_SELECT: 'Select end location for this flight',
	PILOT_SELECT: 'Select pilot for this flight',
	ROW_CHECKBOX: (rowNumber: number) => `Select row ${rowNumber} for upload`,
	SELECT_ALL_CHECKBOX: 'Select all rows for upload',
	VALIDATION_ERROR_FIELD: (fieldName: string) => `${fieldName} field has validation errors`,
	UPLOAD_BUTTON: 'Upload selected flights to server',
	CLEAR_SELECTION_BUTTON: 'Clear all row selections'
};

export const TOAST_MESSAGES = {
	REFERENCE_DATA_LOAD_ERROR:
		'Failed to load reference data. Please check network connection and try again.',
	UPLOAD_COMPLETE: (success: number, failed: number) =>
		`Upload complete: ${success} successful, ${failed} failed`,
	UPLOAD_ALL_SUCCESS: (count: number) => `Successfully uploaded ${count} flights`,
	UPLOAD_CANCELLED: 'Upload cancelled by user'
};

export function getFieldDisplayName(fieldKey: string): string {
	return FIELD_NAMES[fieldKey as keyof typeof FIELD_NAMES] || fieldKey;
}

export function formatMissingFieldsMessage(missingFields: string[]): string {
	const fieldNames = missingFields.map((field) => getFieldDisplayName(field)).join(', ');
	return UPLOAD_MESSAGES.CANNOT_UPLOAD_MISSING_FIELDS(fieldNames);
}
