export function excelDateToJSDate(excelDate: number): Date | null {
	if (isNaN(excelDate)) {
		return null;
	}

	const millisecondsPerDay = 24 * 60 * 60 * 1000;
	const excelEpoch = new Date(1900, 0, 1).getTime();

	const adjustedDays = excelDate > 59 ? excelDate - 2 : excelDate - 1;

	return new Date(excelEpoch + adjustedDays * millisecondsPerDay);
}

export function excelTimeToHHMM(excelTime: number | string): string {
	if (typeof excelTime === 'string') {
		return excelTime;
	}

	if (isNaN(excelTime)) {
		return '00:00';
	}

	const normalizedTime = excelTime % 1;

	const totalMinutes = Math.round(normalizedTime * 24 * 60);
	const hours = Math.floor(totalMinutes / 60);
	const minutes = totalMinutes % 60;

	return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
}

export function combineDateAndTime(dateValue: any, timeValue: any): Date | null {
	let dateObj: Date | null = null;

	if (typeof dateValue === 'number') {
		dateObj = excelDateToJSDate(dateValue);
	} else if (dateValue instanceof Date) {
		dateObj = new Date(dateValue);
	} else if (dateValue && typeof dateValue === 'string') {
		try {
			dateObj = new Date(dateValue);
		} catch (e) {
			return null;
		}
	}

	if (!dateObj || isNaN(dateObj.getTime())) {
		return null;
	}

	if (typeof timeValue === 'number') {
		const hours = Math.floor(timeValue * 24);
		const minutes = Math.floor((timeValue * 24 * 60) % 60);
		dateObj.setHours(hours, minutes, 0, 0);
	} else if (timeValue && typeof timeValue === 'string') {
		const timeMatch = timeValue.match(/(\d{1,2}):(\d{2})/);
		if (timeMatch) {
			const hours = parseInt(timeMatch[1]);
			const minutes = parseInt(timeMatch[2]);
			if (!isNaN(hours) && !isNaN(minutes)) {
				dateObj.setHours(hours, minutes, 0, 0);
			}
		}
	}

	return dateObj;
}

export function timeStringToExcelTime(timeString: string): number | null {
	if (!timeString || typeof timeString !== 'string') {
		return null;
	}

	const timeMatch = timeString.match(/(\d{1,2}):(\d{2})/);
	if (!timeMatch) {
		return null;
	}

	const hours = parseInt(timeMatch[1]);
	const minutes = parseInt(timeMatch[2]);

	if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
		return null;
	}

	return hours / 24 + minutes / (24 * 60);
}

export function isExcelDate(value: any): boolean {
	if (typeof value !== 'number' || isNaN(value)) {
		return false;
	}

	return value >= 1 && value <= 50000;
}

export function isExcelTime(value: any): boolean {
	if (typeof value !== 'number' || isNaN(value)) {
		return false;
	}

	return value >= 0 && value < 1;
}
