import { get } from 'svelte/store';
import { keycloakClient } from '../stores';
import { environment } from '../environment';
import type {
	UnscheduledMaintenanceRequest,
	MaintenanceRequestResponse
} from '../types/maintenance-request.types';

export async function requestUnscheduledMaintenance(
	payload: UnscheduledMaintenanceRequest
): Promise<MaintenanceRequestResponse> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;

	if (!token) {
		throw new Error('Authentication token not found');
	}

	try {
		const url = `${environment.microsoftMsBackendUrl}/ms-teams/send-unscheduled-maintenance-notification`;

		const response = await fetch(url, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Accept: 'application/json',
				Authorization: `Bearer ${token}`
			},
			body: JSON.stringify(payload)
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error(
				'Failed to request maintenance:',
				response.status,
				response.statusText,
				errorText
			);
			throw new Error(`Failed to request maintenance: ${response.status} ${response.statusText}`);
		}

		const data = await response.json();
		return data;
	} catch (error) {
		console.error('Error requesting unscheduled maintenance:', error);
		throw error;
	}
}
