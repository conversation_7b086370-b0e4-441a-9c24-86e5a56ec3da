import { get } from 'svelte/store';
import { keycloakClient } from '../stores';
import { environment } from '../environment';

export interface GliderStatus {
	id: number;
	name: string;
	colorHexcode: string;
	createdAt: string;
	updatedAt: string;
}

export interface GliderStatusLog {
	id: number;
	createdAt: string;
	updatedAt: string;
	gliderName: string;
	userEmail: string;
	note: string;
	newGliderStatus: GliderStatus;
	oldGliderStatus: GliderStatus;
}

export interface UpdateStatusPayload {
	gliderName: string;
	status: string;
	note: string;
	userEmail: string;
}

export async function getAvailableStatuses(timeoutMs: number = 10000): Promise<GliderStatus[]> {
	const keycloak = get(keycloakClient);

	if (keycloak?.isTokenExpired()) {
		try {
			await keycloak.updateToken(30);
		} catch (error) {
			throw new Error('Failed to refresh authentication token');
		}
	}

	const token = keycloak?.token;
	if (!token) {
		throw new Error('Authentication token not found');
	}

	const controller = new AbortController();
	const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

	try {
		const url = `${environment.gliderMsBackendUrl}/glider-statuses`;

		const response = await fetch(url, {
			headers: {
				Authorization: `Bearer ${token}`,
				Accept: 'application/json'
			},
			signal: controller.signal
		});

		clearTimeout(timeoutId);

		if (!response.ok) {
			const errorText = await response.text();
			throw new Error(
				`Failed to fetch statuses: ${response.status} ${response.statusText} - ${errorText}`
			);
		}

		const data = await response.json();

		if (!Array.isArray(data)) {
			throw new Error('Invalid statuses data received from API - expected array');
		}

		const validStatuses = data.filter(
			(status) =>
				status &&
				typeof status.id === 'number' &&
				typeof status.name === 'string' &&
				typeof status.colorHexcode === 'string'
		);

		return validStatuses;
	} catch (error) {
		clearTimeout(timeoutId);

		if (error instanceof Error) {
			if (error.name === 'AbortError') {
				throw new Error(`Request timeout after ${timeoutMs}ms`);
			}
			throw error;
		}

		throw new Error('Unknown error occurred while fetching statuses');
	}
}

export async function getGliderStatusLogs(gliderName: string): Promise<GliderStatusLog[]> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;

	if (!token) {
		console.error('Authentication token not found');
		return [];
	}

	try {
		const url = `${environment.gliderMsBackendUrl}/logs/glider-statuses/glider-name/${encodeURIComponent(gliderName)}`;

		const response = await fetch(url, {
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			}
		});

		if (!response.ok) {
			console.error('Failed to fetch status logs:', response.status, response.statusText);
			return [];
		}

		const data = await response.json();
		return Array.isArray(data) ? data : [];
	} catch (error) {
		console.error('Error fetching status logs:', error);
		return [];
	}
}

export async function updateDroneStatus(payload: UpdateStatusPayload): Promise<any> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;

	if (!token) {
		console.error('Authentication token not found');
		return null;
	}

	try {
		const formattedStatus = formatStatusForApi(payload.status);
		const url = `${environment.gliderMsBackendUrl}/gliders/${encodeURIComponent(payload.gliderName)}/update-status/${formattedStatus}`;

		const queryParams = new URLSearchParams({
			note: payload.note,
			userEmail: payload.userEmail
		});

		const response = await fetch(`${url}?${queryParams}`, {
			method: 'PATCH',
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			},
			body: JSON.stringify({})
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error(
				'Failed to update drone status:',
				response.status,
				response.statusText,
				errorText
			);
			throw new Error(`Failed to update status: ${response.status} ${response.statusText}`);
		}

		const data = await response.json();
		return data;
	} catch (error) {
		console.error('Error updating drone status:', error);
		throw error;
	}
}

export function formatStatusForApi(status: string): string {
	return status
		.toLowerCase()
		.replace(/\s+/g, '-')
		.replace(/[^a-z-]/g, '');
}

export function getStatusColor(status: GliderStatus | null): string {
	if (!status) return '#f44336';
	if (status.colorHexcode) {
		return status.colorHexcode.startsWith('#') ? status.colorHexcode : `#${status.colorHexcode}`;
	}
	const fallbackColors: { [key: string]: string } = {
		ready: '#4caf50',
		grounded: '#ff9800',
		unavailable: '#f44336',
		'maintenance due': '#9c27b0',
		'post-maint. checks': '#2196f3',
		'post-maintenance checks': '#2196f3',
		retired: '#607d8b'
	};

	const statusName = status.name.toLowerCase();
	return fallbackColors[statusName] || '#f44336';
}

export function getDefaultStatus(): GliderStatus {
	return {
		id: 0,
		name: 'Unavailable',
		colorHexcode: 'f44336',
		createdAt: new Date().toISOString(),
		updatedAt: new Date().toISOString()
	};
}

function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
	const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
	return result
		? {
				r: parseInt(result[1], 16),
				g: parseInt(result[2], 16),
				b: parseInt(result[3], 16)
			}
		: null;
}

function getLuminance(r: number, g: number, b: number): number {
	const [rs, gs, bs] = [r, g, b].map((c) => {
		c = c / 255;
		return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
	});
	return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
}

export function getTextColor(backgroundColor: string): string {
	const rgb = hexToRgb(backgroundColor);
	if (!rgb) return '#ffffff';

	const luminance = getLuminance(rgb.r, rgb.g, rgb.b);
	return luminance > 0.5 ? '#000000' : '#ffffff';
}

export function getFlowbiteColor(status: GliderStatus | null): string {
	if (!status || !status.colorHexcode) return 'gray';

	const colorHex = status.colorHexcode.startsWith('#')
		? status.colorHexcode
		: `#${status.colorHexcode}`;
	const rgb = hexToRgb(colorHex);
	if (!rgb) return 'gray';

	return getClosestFlowbiteColor(rgb);
}

function getClosestFlowbiteColor(rgb: { r: number; g: number; b: number }): string {
	const flowbiteColors = {
		red: { r: 239, g: 68, b: 68 },
		orange: { r: 249, g: 115, b: 22 },
		yellow: { r: 234, g: 179, b: 8 },
		green: { r: 34, g: 197, b: 94 },
		blue: { r: 59, g: 130, b: 246 },
		purple: { r: 139, g: 92, b: 246 },
		dark: { r: 75, g: 85, b: 99 },
		gray: { r: 107, g: 114, b: 128 }
	};

	let closestColor = 'gray';
	let minDistance = Infinity;

	for (const [colorName, colorRgb] of Object.entries(flowbiteColors)) {
		const distance = Math.sqrt(
			Math.pow(rgb.r - colorRgb.r, 2) +
				Math.pow(rgb.g - colorRgb.g, 2) +
				Math.pow(rgb.b - colorRgb.b, 2)
		);

		if (distance < minDistance) {
			minDistance = distance;
			closestColor = colorName;
		}
	}

	return closestColor;
}
