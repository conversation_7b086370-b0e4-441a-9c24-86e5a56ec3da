import { get } from 'svelte/store';
import { keycloakClient } from '../stores';
import { environment } from '../environment';

export interface Route {
	start_location_id: number;
	start_location_name: string;
	end_location_id: number;
	end_location_name: string;
	emergency_contact: string;
	known_dangers: string;
	extra_notes: string;
	external_route_id: string;
	customer_id: number;
	id: number;
	customer: {
		name: string;
		id: number;
	};
}

export interface RideStatus {
	id: number;
	name: string;
	description: string;
}

export interface CancelReason {
	name: string;
	description: string;
	id: number;
}

export interface Ride {
	id?: number;
	from_location: number;
	to_location: number;
	departure_time: string;
	arrival_time?: string | null;
	ride_status_id: number;
	glider_id: number;
	glider_name?: string;
	operator_id: string;
	has_package: boolean;
	package_description?: string;
	route_id?: number | null;
	cancel_reason_id?: number | null;
	// Response-only fields (returned by API but not sent in requests)
	route?: Route;
	ride_status?: RideStatus;
	cancel_reason?: CancelReason | null;
}
export interface CreateRideRequest {
	from_location: number;
	to_location: number;
	departure_time: string;
	arrival_time?: string | null;
	ride_status_id: number;
	glider_id: number;
	glider_name?: string;
	operator_id: string;
	has_package: boolean;
	package_description?: string;
	route_id?: number | null;
	cancel_reason_id?: number | null;
}

export async function fetchRides(
	params: {
		skip?: number;
		limit?: number;
		operator_id?: string | null;
		ride_status_id?: number | null;
		glider_name?: string | null;
		route_id?: number | null;
		start_time?: string | null;
		end_time?: string | null;
		customer_id?: number | null;
	} = {},
	options?: { signal?: AbortSignal }
) {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		return [];
	}

	try {
		const queryParams = new URLSearchParams();

		queryParams.append('skip', String(params.skip || 0));
		queryParams.append('limit', String(params.limit || 10));

		if (params.operator_id) queryParams.append('operator_id', params.operator_id);
		if (params.ride_status_id) queryParams.append('ride_status_id', String(params.ride_status_id));
		if (params.glider_name) queryParams.append('glider_name', params.glider_name);
		if (params.route_id) queryParams.append('route_id', String(params.route_id));
		if (params.start_time) queryParams.append('start_time', params.start_time);
		if (params.end_time) queryParams.append('end_time', params.end_time);
		if (params.customer_id) queryParams.append('customer_id', String(params.customer_id));

		const url = `${environment.urlMsRides}/rides?${queryParams.toString()}`;

		const response = await fetch(url, {
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			},
			signal: options?.signal
		});

		if (!response.ok) {
			return [];
		}

		const data = await response.json();

		return data;
	} catch (error) {
		return [];
	}
}

export async function createRide(data: CreateRideRequest): Promise<Ride> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		throw new Error('Authentication token not found');
	}

	try {
		const url = `${environment.urlMsRides}/rides`;

		const response = await fetch(url, {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			},
			body: JSON.stringify(data)
		});

		if (!response.ok) {
			let errorText = `API Error: ${response.status} ${response.statusText}`;
			try {
				const errorBody = await response.text();
				try {
					const parsedError = JSON.parse(errorBody);
					if (typeof parsedError === 'string') {
						errorText = parsedError;
					} else if (parsedError.detail) {
						if (Array.isArray(parsedError.detail)) {
							errorText = parsedError.detail
								.map((err) => `${err.loc?.join('.')}: ${err.msg}`)
								.join(', ');
						} else {
							errorText = parsedError.detail;
						}
					} else {
						errorText = JSON.stringify(parsedError);
					}
				} catch (parseError) {
					errorText = errorBody;
				}
			} catch (e) {
				console.error('Could not read error response:', e);
			}
			throw new Error(errorText);
		}

		const responseData = await response.json();
		return responseData;
	} catch (error) {
		throw error;
	}
}

export async function fetchRideById(rideId: number): Promise<Ride | null> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		return null;
	}

	try {
		const url = `${environment.urlMsRides}/rides/${rideId}`;

		const response = await fetch(url, {
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			}
		});

		if (!response.ok) {
			return null;
		}

		const data = await response.json();
		return data;
	} catch (error) {
		return null;
	}
}

export async function updateRide(rideId: number, data: Partial<Ride>): Promise<boolean> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		throw new Error('Authentication token not found');
	}

	try {
		const url = `${environment.urlMsRides}/rides/${rideId}`;

		const response = await fetch(url, {
			method: 'PATCH',
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			},
			body: JSON.stringify(data)
		});

		if (!response.ok) {
			let errorText = `API Error: ${response.status} ${response.statusText}`;
			try {
				const errorBody = await response.json();
				errorText = errorBody.detail || JSON.stringify(errorBody);
			} catch (e) {}
			throw new Error(errorText);
		}

		return true;
	} catch (error) {
		throw error;
	}
}

export async function deleteRide(rideId: number): Promise<boolean> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		throw new Error('Authentication token not found');
	}

	try {
		const url = `${environment.urlMsRides}/rides/${rideId}`;

		const response = await fetch(url, {
			method: 'DELETE',
			headers: {
				Authorization: `Bearer ${token}`,
				Accept: 'application/json'
			}
		});

		if (!response.ok) {
			let errorText = `API Error: ${response.status} ${response.statusText}`;
			try {
				const errorBody = await response.json();
				errorText = errorBody.detail || JSON.stringify(errorBody);
			} catch (e) {}
			throw new Error(errorText);
		}

		return true;
	} catch (error) {
		throw error;
	}
}

export async function updateRidePackageInfo(
	rideId: number,
	hasPackage: boolean,
	packageDescription: string
): Promise<boolean> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		return false;
	}

	try {
		const url = `${environment.urlMsRides}/rides/${rideId}`;

		const response = await fetch(url, {
			method: 'PATCH',
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			},
			body: JSON.stringify({
				has_package: hasPackage,
				package_description: hasPackage ? packageDescription : ''
			})
		});

		if (!response.ok) {
			return false;
		}

		return true;
	} catch (error) {
		return false;
	}
}

export async function updateRideWithMultipleFields(
	rideId: number,
	updateData: Record<string, any>
): Promise<boolean> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		return false;
	}

	try {
		const url = `${environment.urlMsRides}/rides/${rideId}`;

		const response = await fetch(url, {
			method: 'PATCH',
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			},
			body: JSON.stringify(updateData)
		});

		if (!response.ok) {
			return false;
		}

		const responseData = await response.json();
		return true;
	} catch (error) {
		return false;
	}
}
