import { get } from 'svelte/store';
import { keycloakClient } from '../stores';
import { environment } from '../environment';

export interface Shift {
	pilot_email: string;
	route_id: number;
	start_time: string;
	stop_time: string | null;
	start_location_name?: string;
	end_location_name?: string;
	shift_id?: number;
	id?: number;
	description?: string;
	ride_id?: number;
	created_at?: string;
	updated_at?: string;
}

export interface TimeTrackingEvent {
	route_id: number;
	pilot_email: string;
	event: string;
	shift_id: number;
	event_timestamp: string;
	id: number;
	created_at: string;
}

export interface EditTimeTrackingRequest {
	pilot_email: string;
	route_id: number;
	start_time: string;
	stop_time: string;
	description?: string;
	ride_id?: number;
}

interface ShiftsParams {
	start_date?: string;
	end_date?: string;
	pilot_email?: string;
	route_id?: number;
	ride_id?: number;
	customer_id?: number;
	skip?: number;
	limit?: number;
}

interface Route {
	id: number;
	start_location_name: string;
	end_location_name: string;
}

export async function getShifts(params: ShiftsParams = {}): Promise<Shift[]> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		console.error('Token not found');
		return [];
	}

	try {
		const queryParams = new URLSearchParams();

		if (params.start_date) queryParams.append('start_date', params.start_date);
		if (params.end_date) queryParams.append('end_date', params.end_date);
		if (params.pilot_email) queryParams.append('pilot_email', params.pilot_email);
		if (params.route_id) queryParams.append('route_id', params.route_id.toString());
		if (params.ride_id) queryParams.append('ride_id', params.ride_id.toString());
		if (params.customer_id) queryParams.append('customer_id', params.customer_id.toString());
		if (params.skip !== undefined) queryParams.append('skip', params.skip.toString());
		if (params.limit !== undefined) queryParams.append('limit', params.limit.toString());

		const url = `${environment.urlMsRides}/shifts?${queryParams.toString()}`;

		const response = await fetch(url, {
			method: 'GET',
			headers: {
				Authorization: `Bearer ${token}`
			}
		});

		if (!response.ok) {
			console.error('Error response from API:', response.status, response.statusText);
			return [];
		}

		const data = await response.json();
		return data;
	} catch (error) {
		console.error('Failed to fetch shifts:', error);
		return [];
	}
}

export async function enrichShiftsWithRouteInfo(shifts: Shift[]): Promise<Shift[]> {
	if (!shifts.length) return [];

	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		console.error('Token not found');
		return shifts;
	}

	try {
		const uniqueRouteIds = [];
		const routeIdMap = {};

		for (let i = 0; i < shifts.length; i++) {
			const routeId = shifts[i].route_id;
			if (!routeIdMap[routeId]) {
				routeIdMap[routeId] = true;
				uniqueRouteIds.push(routeId);
			}
		}
		const routesInfo = {};

		for (let i = 0; i < uniqueRouteIds.length; i++) {
			const routeId = uniqueRouteIds[i];
			const url = `${environment.urlMsRides}/routes/${routeId}`;

			try {
				const response = await fetch(url, {
					method: 'GET',
					headers: {
						Authorization: `Bearer ${token}`
					}
				});

				if (response.ok) {
					const routeData = await response.json();
					routesInfo[routeId] = routeData;
				}
			} catch (error) {
				console.error(`Failed to fetch route ${routeId}:`, error);
			}
		}
		const enrichedShifts = [];

		for (let i = 0; i < shifts.length; i++) {
			const shift = { ...shifts[i] };
			const routeInfo = routesInfo[shift.route_id];

			if (routeInfo) {
				shift.start_location_name = routeInfo.start_location_name;
				shift.end_location_name = routeInfo.end_location_name;
			}

			enrichedShifts.push(shift);
		}

		return enrichedShifts;
	} catch (error) {
		console.error('Failed to enrich shifts with route info:', error);
		return shifts;
	}
}

const lastShiftCache: Map<string, { data: Shift | null; ts: number }> = new Map();
const inFlightLastShift: Map<string, Promise<Shift | null>> = new Map();
const LAST_SHIFT_TTL = 30000;

export async function getLastShiftCached(pilotEmail: string): Promise<Shift | null> {
	const now = Date.now();
	const cached = lastShiftCache.get(pilotEmail);
	if (cached && now - cached.ts < LAST_SHIFT_TTL) return cached.data;
	const pending = inFlightLastShift.get(pilotEmail);
	if (pending) return pending;
	const req = getLastShift(pilotEmail)
		.then((data) => {
			lastShiftCache.set(pilotEmail, { data, ts: Date.now() });
			inFlightLastShift.delete(pilotEmail);
			return data;
		})
		.catch((e) => {
			inFlightLastShift.delete(pilotEmail);
			throw e;
		});
	inFlightLastShift.set(pilotEmail, req);
	return req;
}

export async function getLastShift(pilotEmail: string): Promise<Shift | null> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		console.error('Token not found');
		return null;
	}

	try {
		const url = `${environment.urlMsRides}/shifts/last?pilot_email=${encodeURIComponent(pilotEmail)}`;

		const response = await fetch(url, {
			method: 'GET',
			headers: {
				Authorization: `Bearer ${token}`
			}
		});

		if (!response.ok) {
			return null;
		}

		const data = await response.json();
		return data;
	} catch (error) {
		console.error('Failed to fetch last shift:', error);
		return null;
	}
}

export async function startTimeTracking(
	routeId: number,
	pilotEmail: string,
	description?: string,
	rideId?: number
): Promise<TimeTrackingEvent | null> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		console.error('Token not found');
		return null;
	}

	try {
		const queryParams = new URLSearchParams();
		queryParams.append('route_id', routeId.toString());
		queryParams.append('pilot_email', pilotEmail);

		if (description) {
			queryParams.append('description', description);
		}

		if (rideId) {
			queryParams.append('ride_id', rideId.toString());
		}

		const url = `${environment.urlMsRides}/time-tracking/start?${queryParams.toString()}`;

		const response = await fetch(url, {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${token}`
			}
		});

		if (!response.ok) {
			const errorData = await response.json().catch(() => null);
			const errorMessage = errorData?.detail || `HTTP ${response.status}: ${response.statusText}`;
			throw new Error(errorMessage);
		}

		const data = await response.json();
		return data;
	} catch (error) {
		if (error instanceof Error) {
			throw error;
		}
		throw new Error('Unknown error occurred while starting time tracking');
	}
}

export async function stopTimeTracking(
	shiftId: number,
	eventTimestamp?: string
): Promise<TimeTrackingEvent | null> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		console.error('Token not found');
		return null;
	}

	try {
		const queryParams = new URLSearchParams();
		queryParams.append('shift_id', shiftId.toString());

		if (eventTimestamp) {
			queryParams.append('event_timestamp', eventTimestamp);
		}

		const url = `${environment.urlMsRides}/time-tracking/stop?${queryParams.toString()}`;

		const response = await fetch(url, {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${token}`
			}
		});

		if (!response.ok) {
			const errorData = await response.json().catch(() => null);
			const errorMessage = errorData?.detail || `HTTP ${response.status}: ${response.statusText}`;
			throw new Error(errorMessage);
		}

		const data = await response.json();
		return data;
	} catch (error) {
		if (error instanceof Error) {
			throw error;
		}
		throw new Error('Unknown error occurred while stopping time tracking');
	}
}

export async function editTimeTracking(
	shiftId: number,
	editData: EditTimeTrackingRequest,
	eventTimestamp?: string
): Promise<TimeTrackingEvent | null> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		console.error('Token not found');
		return null;
	}

	try {
		const queryParams = new URLSearchParams();
		queryParams.append('shift_id', shiftId.toString());

		if (eventTimestamp) {
			queryParams.append('event_timestamp', eventTimestamp);
		}

		const url = `${environment.urlMsRides}/time-tracking/edit?${queryParams.toString()}`;

		const response = await fetch(url, {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(editData)
		});

		if (!response.ok) {
			const errorData = await response.json().catch(() => null);
			const errorMessage = errorData?.detail || `HTTP ${response.status}: ${response.statusText}`;
			throw new Error(errorMessage);
		}

		const data = await response.json();
		return data;
	} catch (error) {
		if (error instanceof Error) {
			throw error;
		}
		throw new Error('Unknown error occurred while editing time tracking');
	}
}

export async function getUniquePilots(): Promise<string[]> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		return [];
	}

	try {
		const shifts = await getShifts({ limit: 1000 });
		const uniquePilotsSet = new Set(shifts.map((shift) => shift.pilot_email));
		const uniquePilots = Array.from(uniquePilotsSet);

		return uniquePilots;
	} catch (error) {
		return [];
	}
}
