import { get } from 'svelte/store';
import { keycloakClient } from '../stores';
import { environment } from '../environment';

export interface Glider {
	id: number;
	line: string;
	generation: string;
	number: string;
	name: string;
	pixhawkUuid: string;
	gliderMode: any;
	gliderStatus: any;
	autopilotSoftwareVersion: any;
	desiredAutopilotSoftwareVersion: any;
	jetsonSoftwareVersion: any;
	desiredJetsonSoftwareVersion: any;
	ftsPixhawkSoftwareVersion: any;
	desiredFtsPixhawkSoftwareVersion: any;
	ftsRaspiSoftwareVersion: any;
	desiredFtsRaspiSoftwareVersion: any;
	company: any;
	region: any;
	vpnIp: string;
	vpnNetworkId: string;
	manufacturingDate: string;
	registrationCode: string;
	registrationComplete: boolean;
	inUse: boolean;
	designDeviation: string;
	designComplianceRecord: string;
	totalFlightTimeInSeconds: number;
	totalFlightTimeSinceLastMaintenanceInSeconds: number;
	createdAt: string;
	updatedAt: string;
	flightPreConditionRead: any;
}

export interface GliderMaintenance {
	id: number;
	dueDate: string;
	completedDate?: string;
	isScheduled: boolean;
	notes?: string;
	maintenanceStaff?: string;
	postMaintenanceChecklistDone: boolean;
	maintenanceType: {
		id: number;
		name: string;
	};
}

export interface GliderStatusLog {
	id: number;
	createdAt: string;
	updatedAt: string;
	gliderName: string;
	userEmail: string;
	note?: string;
	newGliderStatus: any;
	oldGliderStatus: any;
}

export interface FlightData {
	id: number;
	start_time: number;
	duration: number;
	distance_meters?: number;
	start_location?: string;
	end_location?: string;
}

export interface IncidentData {
	id: number;
	name: string;
	aircraft_damage?: string;
	description?: string;
	injuries?: string;
}

export async function fetchGliders(): Promise<Glider[]> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		return [];
	}

	try {
		const url = `${environment.gliderMsBackendUrl}/gliders`;

		const response = await fetch(url, {
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			}
		});

		if (!response.ok) {
			return [];
		}

		const data = await response.json();
		return data;
	} catch (error) {
		return [];
	}
}

export async function fetchGliderById(
	gliderId: number,
	timeoutMs: number = 10000
): Promise<Glider | null> {
	const keycloak = get(keycloakClient);
	if (keycloak?.isTokenExpired()) {
		try {
			await keycloak.updateToken(30);
		} catch (error) {
			throw new Error('Failed to refresh authentication token');
		}
	}

	const token = keycloak?.token;
	if (!token) {
		throw new Error('Authentication token not found');
	}
	const controller = new AbortController();
	const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

	try {
		const url = `${environment.gliderMsBackendUrl}/gliders/${gliderId}`;

		const response = await fetch(url, {
			headers: {
				Authorization: `Bearer ${token}`,
				Accept: 'application/json'
			},
			signal: controller.signal
		});
		clearTimeout(timeoutId);
		if (response.status === 404) {
			return null;
		}

		if (!response.ok) {
			const errorText = await response.text();
			throw new Error(
				`Failed to fetch glider: ${response.status} ${response.statusText} - ${errorText}`
			);
		}

		const data = await response.json();
		if (!data || typeof data.id !== 'number') {
			throw new Error('Invalid glider data received from API');
		}

		return data;
	} catch (error) {
		clearTimeout(timeoutId);

		if (error instanceof Error) {
			if (error.name === 'AbortError') {
				throw new Error(`Request timeout after ${timeoutMs}ms`);
			}
			throw error;
		}
		throw new Error('Unknown error occurred while fetching glider');
	}
}

export async function updateGlider(id: number, data: Partial<Glider>): Promise<Glider | null> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		console.error('Token not found');
		return null;
	}

	try {
		const url = `${environment.gliderMsBackendUrl}/gliders/${id}`;

		const response = await fetch(url, {
			method: 'PUT',
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			},
			body: JSON.stringify(data)
		});

		if (!response.ok) {
			console.error('Failed to update glider:', response.status, response.statusText);
			return null;
		}

		const responseData = await response.json();
		return responseData;
	} catch (error) {
		console.error('Error updating glider:', error);
		return null;
	}
}

export async function getSoftwareVersionsByTypeId(typeId: number): Promise<any[]> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		console.error('Token not found');
		return [];
	}

	try {
		const url = `${environment.gliderMsBackendUrl}/software-versions/software-version-type/${typeId}`;

		const response = await fetch(url, {
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			}
		});

		if (!response.ok) {
			console.error('Failed to fetch software versions:', response.status, response.statusText);
			return [];
		}

		const data = await response.json();
		return data;
	} catch (error) {
		console.error('Error fetching software versions:', error);
		return [];
	}
}

export async function updateGliderSoftwareVersion(
	gliderId: number,
	updateField: string,
	versionId: number
): Promise<Glider | null> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		console.error('Token not found');
		return null;
	}

	try {
		const url = `${environment.gliderMsBackendUrl}/gliders/${gliderId}`;
		const payload = { [updateField]: versionId };

		const response = await fetch(url, {
			method: 'PATCH',
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			},
			body: JSON.stringify(payload)
		});

		if (!response.ok) {
			console.error(
				'Failed to update glider software version:',
				response.status,
				response.statusText
			);
			return null;
		}

		const responseData = await response.json();
		return responseData;
	} catch (error) {
		console.error('Error updating glider software version:', error);
		return null;
	}
}

export async function fetchMaintenancesByGliderId(gliderId: number): Promise<GliderMaintenance[]> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		console.error('Token not found');
		return [];
	}

	try {
		const url = `${environment.gliderMsBackendUrl}/maintenances/glider/${gliderId}`;

		const response = await fetch(url, {
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			}
		});

		if (!response.ok) {
			console.error('Failed to fetch maintenances:', response.status, response.statusText);
			return [];
		}

		const data = await response.json();
		return data;
	} catch (error) {
		console.error('Error fetching maintenances:', error);
		return [];
	}
}

export async function fetchGliderStatusLogs(gliderName: string): Promise<GliderStatusLog[]> {
	const keycloak = get(keycloakClient);
	const token = keycloak?.token;
	if (!token) {
		console.error('Token not found');
		return [];
	}

	try {
		const url = `${environment.gliderMsBackendUrl}/logs/glider-statuses/glider-name/${gliderName}`;

		const response = await fetch(url, {
			headers: {
				Authorization: `Bearer ${token}`,
				'Content-Type': 'application/json',
				Accept: 'application/json'
			}
		});

		if (!response.ok) {
			console.error('Failed to fetch status logs:', response.status, response.statusText);
			return [];
		}

		const data = await response.json();
		return data;
	} catch (error) {
		console.error('Error fetching status logs:', error);
		return [];
	}
}

export async function fetchFlightsByGlider(gliderName: string): Promise<FlightData[]> {
	try {
		const url = `${environment.flightReviewBackendUrl}/api/browse?glider=${encodeURIComponent(gliderName)}`;

		const response = await fetch(url, {
			headers: {
				'Content-Type': 'application/json',
				Accept: 'application/json'
			}
		});

		if (!response.ok) {
			console.error('Failed to fetch flights:', response.status, response.statusText);
			return [];
		}

		const data = await response.json();
		return data || [];
	} catch (error) {
		console.error('Error fetching flights:', error);
		return [];
	}
}

export async function fetchIncidents(): Promise<IncidentData[]> {
	try {
		const url = `${environment.flightReviewBackendUrl}/api/incident`;

		const response = await fetch(url, {
			headers: {
				'Content-Type': 'application/json',
				Accept: 'application/json'
			}
		});

		if (!response.ok) {
			console.error('Failed to fetch incidents:', response.status, response.statusText);
			return [];
		}

		const data = await response.json();
		return data || [];
	} catch (error) {
		console.error('Error fetching incidents:', error);
		return [];
	}
}
