import { describe, it, expect, vi, beforeEach } from 'vitest';
import { requestUnscheduledMaintenance } from '../maintenance-request.service';
import type { UnscheduledMaintenanceRequest } from '../../types/maintenance-request.types';

vi.mock('../../stores', () => ({
	keycloakClient: {
		subscribe: vi.fn(),
		set: vi.fn(),
		update: vi.fn()
	}
}));

vi.mock('../../environment', () => ({
	environment: {
		microsoftMsBackendUrl: 'https://microsoft.test.com'
	}
}));

vi.mock('svelte/store', () => ({
	get: vi.fn(() => ({
		token: 'mock-token'
	}))
}));

global.fetch = vi.fn();

describe('maintenance-request.service', () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	describe('requestUnscheduledMaintenance', () => {
		const mockPayload: UnscheduledMaintenanceRequest = {
			glider_name: 'M01-15',
			user_email: '<EMAIL>',
			maintenance_reasons: 'Incident',
			extra_notes: 'Test maintenance request'
		};

		it('should successfully send maintenance request', async () => {
			const mockResponse = { message: 'Success' };
			(global.fetch as any).mockResolvedValueOnce({
				ok: true,
				json: () => Promise.resolve(mockResponse)
			});

			const result = await requestUnscheduledMaintenance(mockPayload);

			expect(fetch).toHaveBeenCalledWith(
				'https://microsoft.test.com/ms-teams/send-unscheduled-maintenance-notification',
				{
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
						Accept: 'application/json',
						Authorization: 'Bearer mock-token'
					},
					body: JSON.stringify(mockPayload)
				}
			);

			expect(result).toEqual(mockResponse);
		});

		it('should throw error when token is not available', async () => {
			const { get } = await import('svelte/store');
			(get as any).mockReturnValueOnce(null);

			await expect(requestUnscheduledMaintenance(mockPayload)).rejects.toThrow(
				'Authentication token not found'
			);
		});

		it('should throw error when API returns error', async () => {
			(global.fetch as any).mockResolvedValueOnce({
				ok: false,
				status: 500,
				statusText: 'Internal Server Error',
				text: () => Promise.resolve('Server error')
			});

			await expect(requestUnscheduledMaintenance(mockPayload)).rejects.toThrow(
				'Failed to request maintenance: 500 Internal Server Error'
			);
		});
	});
});
