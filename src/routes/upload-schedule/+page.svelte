<script lang="ts">
	import { onMount, tick } from 'svelte';
	import { read, utils } from 'xlsx';
	import Dropzone from 'svelte-file-dropzone';
	import { toast } from 'svelte-sonner';
	import { keycloakClient } from '$lib/stores';
	import { get } from 'svelte/store';
	import { createRide } from '$lib/services/rides.service';
	import { fetchUsers } from '$lib/services/users.service';
	import { fetchRideStatuses } from '$lib/services/ride-status.service';
	import { fetchCancelReasons } from '$lib/services/cancel-reasons.service';
	import { fetchRoutes } from '$lib/services/routes.service';
	import { fetchGliders } from '$lib/services/gliders.service';
	import { fetchLocations } from '$lib/services/locations.service';
	import {
		getTimezoneDetectionStatus,
		getTimezoneDisplayName,
		parseDateDMY
	} from '$lib/utils/datetime';
	import {
		getMissingFieldsSummary,
		getRowsWithErrors,
		hasFieldError,
		hasValidationErrors,
		validateScheduleRow
	} from '$lib/utils/validation';
	import {
		ARIA_LABELS,
		formatMissingFieldsMessage,
		TIMEZONE_MESSAGES,
		TOAST_MESSAGES,
		UPLOAD_MESSAGES
	} from '$lib/utils/messages';

	let files: File[] = [];
	let workbook: any = null;
	let sheetNames: string[] = [];
	let selectedSheet = '';
	let parsedData: any[] = [];
	let validRows: any[] = [];
	let isLoading = false;
	let routes: any[] = [];
	let routeMap: Map<string, any> = new Map();
	let cancelReasons: any[] = [];
	let cancelReasonsMapById: Map<number, any> = new Map();
	let isUploading = false;
	let isValidating = false;
	let gliders: any[] = [];
	let locations: any[] = [];
	let pilots: any[] = [];
	let rideStatuses: any[] = [];
	let isApiError = false;
	let apiErrorDetails = '';

	let locationsMapById = new Map();
	let locationsMapByName = new Map();
	let glidersMapById = new Map();
	let glidersMapByName = new Map();
	let pilotsMapById = new Map();

	let rowSelections: { [index: number]: boolean } = {};
	let selectAll = false;
	let checkboxKey = 0;

	$: selectedCount = Object.values(rowSelections).filter(Boolean).length;
	let showConfirmModal = false;
	let uploadSuccess = false;
	let uploadStats = { total: 0, success: 0, failed: 0 };
	let lastError = '';

	let didInitialIdMapping = false;

	let showErrorDetails = false;
	let userTimezone = 'UTC';
	let timezoneDisplayName = 'UTC';
	let timezoneDetectionFailed = false;
	let timezoneError = '';

	function getSelectedCount() {
		return Object.values(rowSelections).filter(Boolean).length;
	}

	$: if (
		!didInitialIdMapping &&
		gliders.length > 0 &&
		locations.length > 0 &&
		pilots.length > 0 &&
		validRows.length > 0
	) {
		const mappedRows = validRows.map((row) => {
			const r = { ...row };
			mapRowDataToIds(r);
			return r;
		});
		validRows = mappedRows;
		didInitialIdMapping = true;
	}

	onMount(async () => {
		const timezoneStatus = getTimezoneDetectionStatus();
		userTimezone = timezoneStatus.timezone;
		timezoneDisplayName = getTimezoneDisplayName(userTimezone);
		timezoneDetectionFailed = !timezoneStatus.isDetected;
		timezoneError = timezoneStatus.error || '';

		if (timezoneDetectionFailed) {
			console.warn('Timezone detection failed:', timezoneError);
		}

		try {
			await Promise.all([
				loadGliders(),
				loadLocations(),
				loadPilots(),
				fetchRideStatuses().then((data) => {
					rideStatuses = data;
				}),
				fetchRoutes().then((data) => {
					routes = data || [];
					routeMap = new Map(
						routes.map((r) => [
							`${String(r.start_location_name || '')
								.toLowerCase()
								.trim()}|${String(r.end_location_name || '')
								.toLowerCase()
								.trim()}`,
							r
						])
					);
				}),
				fetchCancelReasons().then((data) => {
					cancelReasons = data || [];
					cancelReasonsMapById = new Map(cancelReasons.map((c) => [Number(c.id), c]));
				})
			]);
			isApiError = false;
		} catch (error) {
			isApiError = true;
			apiErrorDetails = error.message || TOAST_MESSAGES.REFERENCE_DATA_LOAD_ERROR;
			toast.error(TOAST_MESSAGES.REFERENCE_DATA_LOAD_ERROR);
		}
	});

	async function loadGliders() {
		gliders = await fetchGliders();
		glidersMapById = new Map(gliders.map((g) => [String(g.id), g]));
		glidersMapByName = new Map(gliders.map((g) => [g.name, g]));
	}

	async function loadLocations() {
		locations = await fetchLocations();
		locationsMapById = new Map(locations.map((l) => [String(l.id), l]));
		locationsMapByName = new Map(locations.map((l) => [l.name, l]));
	}

	async function loadPilots() {
		pilots = await fetchUsers();
		pilotsMapById = new Map(pilots.map((p) => [String(p.id), p]));
	}

	function handleFilesSelect(e: any) {
		const { acceptedFiles } = e.detail;
		files = acceptedFiles;

		if (files.length > 0) {
			readExcelFile(files[0]);
		}
	}

	async function readExcelFile(file: File) {
		isLoading = true;
		try {
			const data = await file.arrayBuffer();
			workbook = read(data);
			sheetNames = workbook.SheetNames;
			selectedSheet = sheetNames[0] || '';

			if (selectedSheet) {
				parseSheet();
			}
		} catch (error) {
			toast.error('Error reading Excel file', error);
		} finally {
			isLoading = false;
		}
	}

	function parseSheet() {
		if (!workbook || !selectedSheet) return;

		try {
			const sheet = workbook.Sheets[selectedSheet];
			const rawData = utils.sheet_to_json(sheet, { raw: true, defval: null });

			const filteredData = filterValidRows(rawData);
			const isTestLogFile = determineFileType(filteredData);

			parsedData = filteredData.map((row, index) => {
				return isTestLogFile ? normalizeTestLogRow(row) : normalizeRegularRow(row);
			});

			propagateDateToEmptyRows();
			initializeRowsWithoutValidation();
		} catch (error) {
			console.log('error', error);
			toast.error('Error parsing Excel sheet');
		}
	}

	function isFieldEmpty(value: any): boolean {
		return value === undefined || value === null || value === '';
	}

	function countNonEmptyFields(row: any): number {
		return Object.keys(row).filter((key) => !isFieldEmpty(row[key])).length;
	}

	function filterValidRows(data: any[]): any[] {
		return data.filter((row) => {
			const nonEmptyFieldsCount = countNonEmptyFields(row);
			return nonEmptyFieldsCount >= 3;
		});
	}

	function determineFileType(data: any[]): boolean {
		if (data.length === 0) return false;

		const firstRow = data[0];
		const testLogFields = ['Date', 'Plug Battery @', 'pilot_id', 'Ground Crew'];

		return testLogFields.some((field) => firstRow[field] !== undefined);
	}

	function normalizeTestLogRow(row: any): any {
		const normalizedRow: any = {};

		const fieldMappings = {
			start_location_name: 'start_location_name',
			end_location_name: 'end_location_name',
			Date: 'date',
			scheduled_start_time: 'scheduled_start_time',
			glider_name: 'glider_name',
			notes: 'notes',
			duration: 'duration',
			scheduled_end_time: 'scheduled_end_time'
		};

		Object.entries(fieldMappings).forEach(([sourceField, targetField]) => {
			if (row[sourceField] !== undefined) {
				normalizedRow[targetField] = row[sourceField];
			}
		});

		if (row['pilot_id'] !== undefined) {
			const pilotValue = row['pilot_id'];
			if (typeof pilotValue === 'string' && pilotValue.includes('@')) {
				normalizedRow.pilot_email = pilotValue;
			} else {
				normalizedRow.pilot_id = pilotValue;
			}
		}

		if (!normalizedRow.pilot_email) {
			const pilotRelatedFields = ['pilot_id', 'pilot_email', 'pilot', 'operator'];
			Object.keys(row).forEach((key) => {
				const value = row[key];
				if (
					pilotRelatedFields.includes(key.toLowerCase()) &&
					typeof value === 'string' &&
					value.includes('@jedsy.com')
				) {
					const crFields = ['cancel_reason', 'cancel reason', 'cancel_reason_name'];
					for (const f of crFields) {
						if (normalizedRow[f] && typeof normalizedRow[f] === 'string') {
							normalizedRow.cancel_reason_name = normalizedRow[f];
							break;
						}
					}

					normalizedRow.pilot_email = value;
				}
			});
		}

		return normalizedRow;
	}

	function normalizeRegularRow(row: any): any {
		const normalizedRow: any = {};

		// Convert all keys to lowercase
		Object.keys(row).forEach((key) => {
			normalizedRow[key.toLowerCase()] = row[key];
		});

		// Process pilot information
		extractPilotEmail(normalizedRow);
		extractPilotName(normalizedRow);
		extractPilotId(normalizedRow);

		return normalizedRow;
	}

	function extractPilotEmail(normalizedRow: any): void {
		const pilotEmailFields = [
			'pilot_email',
			'pilot email',
			'email',
			'operator',
			'operator_email',
			'pilot'
		];

		for (const field of pilotEmailFields) {
			if (normalizedRow[field] && typeof normalizedRow[field] === 'string') {
				const fieldValue = normalizedRow[field].trim();
				if (fieldValue.includes('@')) {
					const emails = extractEmailsFromString(fieldValue);
					if (emails.length > 0) {
						normalizedRow.pilot_email = emails[0];
						break;
					}
				} else if (fieldValue) {
					normalizedRow.pilot_name = fieldValue;
				}
			}
		}
	}

	function extractPilotName(normalizedRow: any): void {
		const pilotNameFields = ['pilot_name', 'pilot name', 'pilot_full_name', 'operator_name'];

		for (const field of pilotNameFields) {
			if (normalizedRow[field] && typeof normalizedRow[field] === 'string') {
				normalizedRow.pilot_name = normalizedRow[field];
				break;
			}
		}
	}

	function extractPilotId(normalizedRow: any): void {
		const pilotIdFields = ['pilot_id', 'pilot id', 'operator_id'];

		for (const field of pilotIdFields) {
			if (normalizedRow[field] && typeof normalizedRow[field] === 'string') {
				const fieldValue = normalizedRow[field].trim();
				if (fieldValue.includes('@')) {
					const emails = extractEmailsFromString(fieldValue);
					if (emails.length > 0) {
						normalizedRow.pilot_email = emails[0];
					}
				} else {
					normalizedRow.pilot_id = fieldValue;
				}
				break;
			}
		}
	}

	function extractEmailsFromString(str: string): string[] {
		return str
			.split(/[/,;]/)
			.map((email) => email.trim())
			.filter((email) => email.includes('@'));
	}

	function propagateDateToEmptyRows() {
		if (parsedData.length === 0) return;

		let firstRowWithDate = null;
		let commonDate = null;

		for (let i = 0; i < parsedData.length; i++) {
			const row = parsedData[i];
			if (row.date !== undefined && row.date !== null) {
				firstRowWithDate = i;
				commonDate = row.date;
				break;
			}

			if (row.Date !== undefined && row.Date !== null) {
				firstRowWithDate = i;
				commonDate = row.Date;
				row.date = row.Date;
				break;
			}
		}

		if (firstRowWithDate !== null && commonDate) {
			for (let i = 0; i < parsedData.length; i++) {
				if (i !== firstRowWithDate && !parsedData[i].date) {
					parsedData[i].date = commonDate;
				}
			}
		}

		for (let i = 0; i < parsedData.length; i++) {
			const row = parsedData[i];

			if (!row.scheduled_start_time) {
				const timeFields = ['time', 'start_time', 'scheduled_start', 'departure'];

				for (const field of timeFields) {
					if (row[field] && typeof row[field] === 'string' && /^\d{1,2}:\d{2}$/.test(row[field])) {
						const [hours, minutes] = row[field].split(':').map(Number);
						row.scheduled_start_time = hours / 24 + minutes / (24 * 60);
						break;
					}
				}

				if (!row.scheduled_start_time && typeof row['scheduled_start_time'] === 'number') {
					row.scheduled_start_time = row['scheduled_start_time'];
				}
			}
		}
	}

	function excelDateToJSDate(excelDate) {
		if (typeof excelDate === 'number') {
			const millisecondsPerDay = 24 * 60 * 60 * 1000;
			return new Date((excelDate - 25569) * millisecondsPerDay);
		}
		return null;
	}

	function excelTimeToHHMM(excelTime) {
		if (typeof excelTime === 'number') {
			const totalMinutes = Math.round(excelTime * 24 * 60);
			const hours = Math.floor(totalMinutes / 60);
			const minutes = totalMinutes % 60;
			return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
		}
		return excelTime;
	}

	function mapRowDataToIds(row) {
		if (row.glider_name && !row.glider_id) {
			const name = String(row.glider_name).trim();
			let glider = glidersMapByName.get(name);
			if (!glider) {
				glider = gliders.find((g) => g.name && g.name.toLowerCase() === name.toLowerCase());
			}
			if (glider) {
				row.glider_id = String(glider.id);
			}
		}
		if (row.start_location_name && !row.start_location_id) {
			const name = String(row.start_location_name).trim();
			let location = locationsMapByName.get(name);
			if (!location) {
				location = locations.find((l) => l.name && l.name.toLowerCase() === name.toLowerCase());
			}
			if (location) {
				row.start_location_id = String(location.id);
			}
		}

		if (row.end_location_name && !row.end_location_id) {
			const name = String(row.end_location_name).trim();
			let location = locationsMapByName.get(name);
			if (!location) {
				location = locations.find((l) => l.name && l.name.toLowerCase() === name.toLowerCase());
			}
			if (location) {
				row.end_location_id = String(location.id);
			}
		}

		if ((row.pilot_email || row.pilot_name) && !row.pilot_id) {
			let pilot = null;

			if (row.pilot_email) {
				pilot = pilots.find((p) => p.email === row.pilot_email);
			}

			if (!pilot && row.pilot_name) {
				pilot = pilots.find(
					(p) =>
						`${p.firstName} ${p.lastName}` === row.pilot_name ||
						p.firstName === row.pilot_name ||
						p.lastName === row.pilot_name
				);
			}

			if (pilot) {
				row.pilot_id = String(pilot.id);
			}
		}
	}

	function initializeRowsWithoutValidation() {
		validRows = parsedData.map((row, index) => {
			const processedRow = {
				...row,
				_validationErrors: [],
				_missingFields: []
			};

			if (gliders.length > 0 && locations.length > 0 && pilots.length > 0) {
				mapRowDataToIds(processedRow);
			}

			let baseDate = null;
			let startDateTime = null;
			let endDateTime = null;

			if (processedRow.scheduled_start_time instanceof Date) {
				startDateTime = processedRow.scheduled_start_time;
				baseDate = new Date(startDateTime);
				baseDate.setHours(0, 0, 0, 0);
			} else {
				if (typeof processedRow.date === 'number') {
					baseDate = excelDateToJSDate(processedRow.date);
				} else if (processedRow.date instanceof Date) {
					baseDate = new Date(processedRow.date);
				} else if (processedRow.date && typeof processedRow.date === 'string') {
					const parsedLocal = parseDateDMY(processedRow.date);
					baseDate = parsedLocal && !isNaN(parsedLocal.getTime()) ? parsedLocal : null;
				}

				if (baseDate && !isNaN(baseDate.getTime())) {
					if (typeof processedRow.scheduled_start_time === 'number') {
						const hours = Math.floor(processedRow.scheduled_start_time * 24);
						const minutes = Math.floor((processedRow.scheduled_start_time * 24 * 60) % 60);

						startDateTime = new Date(baseDate);
						startDateTime.setHours(hours, minutes, 0, 0);
					} else if (typeof processedRow.scheduled_start_time === 'string') {
						const timeMatch = processedRow.scheduled_start_time.match(/(\d{1,2}):(\d{2})/);
						if (timeMatch) {
							const hours = parseInt(timeMatch[1]);
							const minutes = parseInt(timeMatch[2]);
							startDateTime = new Date(baseDate);
							startDateTime.setHours(hours, minutes, 0, 0);
						}
					} else if (processedRow.scheduled_start_time instanceof Date) {
						startDateTime = processedRow.scheduled_start_time;
					}
				}
			}

			if (!baseDate || isNaN(baseDate.getTime())) {
				baseDate = new Date();
				baseDate.setHours(0, 0, 0, 0);
			}

			if (!startDateTime || isNaN(startDateTime.getTime())) {
				startDateTime = new Date(baseDate);
				startDateTime.setHours(9, 0, 0, 0);
			}

			processedRow.date = baseDate;
			processedRow.scheduled_start_time = startDateTime;
			processedRow.dateString = formatDateForInput(baseDate);
			processedRow.startTimeString = formatTimeForInput(startDateTime);

			if (processedRow.scheduled_end_time instanceof Date) {
				endDateTime = processedRow.scheduled_end_time;
			} else if (typeof processedRow.scheduled_end_time === 'number' && baseDate) {
				const hours = Math.floor(processedRow.scheduled_end_time * 24);
				const minutes = Math.floor((processedRow.scheduled_end_time * 24 * 60) % 60);

				endDateTime = new Date(baseDate);
				endDateTime.setHours(hours, minutes, 0, 0);
			} else if (typeof processedRow.scheduled_end_time === 'string' && baseDate) {
				const timeMatch = processedRow.scheduled_end_time.match(/(\d{1,2}):(\d{2})/);
				if (timeMatch) {
					const hours = parseInt(timeMatch[1]);
					const minutes = parseInt(timeMatch[2]);
					endDateTime = new Date(baseDate);
					endDateTime.setHours(hours, minutes, 0, 0);
				}
			}

			if (endDateTime && !isNaN(endDateTime.getTime())) {
				processedRow.scheduled_end_time = endDateTime;
				processedRow.endTimeString = formatTimeForInput(endDateTime);
			} else {
				processedRow.scheduled_end_time = null;
				processedRow.endTimeString = '';
			}

			return processedRow;
		});
		initializeSelection();
	}

	function validateSelectedRows() {
		const selectedIndices = getSelectedRows();
		if (selectedIndices.length === 0) {
			return [];
		}

		if (isApiError || (gliders.length === 0 && locations.length === 0 && pilots.length === 0)) {
			toast.error(UPLOAD_MESSAGES.CANNOT_VALIDATE_NO_DATA);
			return [];
		}

		const validationContext = {
			gliders,
			locations,
			pilots,
			glidersMapByName,
			glidersMapById,
			locationsMapByName,
			locationsMapById,
			pilotsMapById
		};

		const validatedRows = [];
		selectedIndices.forEach((index) => {
			const row = validRows[index];
			const preservedFields = {
				glider_id: row.glider_id,
				glider_name: row.glider_name,
				start_location_id: row.start_location_id,
				start_location_name: row.start_location_name,
				end_location_id: row.end_location_id,
				end_location_name: row.end_location_name,
				pilot_id: row.pilot_id,
				dateString: row.dateString,
				startTimeString: row.startTimeString,
				endTimeString: row.endTimeString,
				date: row.date,
				scheduled_start_time: row.scheduled_start_time,
				scheduled_end_time: row.scheduled_end_time
			};

			const validatedRow = validateScheduleRow(row, validationContext);
			validatedRow.glider_id = preservedFields.glider_id;
			validatedRow.glider_name = preservedFields.glider_name;
			validatedRow.start_location_id = preservedFields.start_location_id;
			validatedRow.start_location_name = preservedFields.start_location_name;
			validatedRow.end_location_id = preservedFields.end_location_id;
			validatedRow.end_location_name = preservedFields.end_location_name;
			validatedRow.pilot_id = preservedFields.pilot_id;
			validatedRow.dateString = preservedFields.dateString;
			validatedRow.startTimeString = preservedFields.startTimeString;
			validatedRow.endTimeString = preservedFields.endTimeString;
			validatedRow.date = preservedFields.date;
			validatedRow.scheduled_start_time = preservedFields.scheduled_start_time;
			validatedRow.scheduled_end_time = preservedFields.scheduled_end_time;

			validRows[index] = validatedRow;
			validatedRows.push(validatedRow);
		});

		validRows = [...validRows];
		return validatedRows;
	}

	function validateSingleRow(index: number) {
		if (isApiError || (gliders.length === 0 && locations.length === 0 && pilots.length === 0)) {
			return;
		}

		const validationContext = {
			gliders,
			locations,
			pilots,
			glidersMapByName,
			glidersMapById,
			locationsMapByName,
			locationsMapById,
			pilotsMapById
		};

		const row = validRows[index];

		const preservedFields = {
			glider_id: row.glider_id,
			glider_name: row.glider_name,
			start_location_id: row.start_location_id,
			start_location_name: row.start_location_name,
			end_location_id: row.end_location_id,
			end_location_name: row.end_location_name,
			pilot_id: row.pilot_id,
			dateString: row.dateString,
			startTimeString: row.startTimeString,
			endTimeString: row.endTimeString,
			date: row.date,
			scheduled_start_time: row.scheduled_start_time,
			scheduled_end_time: row.scheduled_end_time
		};

		const validatedRow = validateScheduleRow(row, validationContext);

		validatedRow.glider_id = preservedFields.glider_id;
		validatedRow.glider_name = preservedFields.glider_name;
		validatedRow.start_location_id = preservedFields.start_location_id;
		validatedRow.start_location_name = preservedFields.start_location_name;
		validatedRow.end_location_id = preservedFields.end_location_id;
		validatedRow.end_location_name = preservedFields.end_location_name;
		validatedRow.pilot_id = preservedFields.pilot_id;
		validatedRow.dateString = preservedFields.dateString;
		validatedRow.startTimeString = preservedFields.startTimeString;
		validatedRow.endTimeString = preservedFields.endTimeString;
		validatedRow.date = preservedFields.date;
		validatedRow.scheduled_start_time = preservedFields.scheduled_start_time;
		validatedRow.scheduled_end_time = preservedFields.scheduled_end_time;

		validRows[index] = validatedRow;
		validRows = [...validRows];
	}

	function initializeSelection() {
		rowSelections = {};
		validRows.forEach((_, index) => {
			rowSelections[index] = false;
		});
		updateSelectAllState();
	}

	function updateSelectAllState() {
		const selectedCount = Object.values(rowSelections).filter(Boolean).length;
		selectAll = selectedCount === validRows.length && validRows.length > 0;
	}

	function toggleSelectAll() {
		selectAll = !selectAll;
		handleSelectAllChange();
	}

	function handleSelectAllChange() {
		// Create a completely new object to ensure reactivity
		const newSelections = {};
		validRows.forEach((_, index) => {
			newSelections[index] = selectAll;
		});

		rowSelections = newSelections;

		// Force re-render with key update
		checkboxKey += 1;

		// Use tick to ensure DOM updates
		tick().then(() => {
			checkboxKey += 1;
		});
	}

	function isRowSelected(index: number) {
		return !!rowSelections[index];
	}

	function toggleRowSelection(index: number) {
		const newSelections = { ...rowSelections };
		newSelections[index] = !newSelections[index];
		rowSelections = newSelections;
		updateSelectAllState();
		checkboxKey += 1;
	}

	function handleRowSelectionChange(index: number) {
		// rowSelections[index] is already updated by bind:checked
		rowSelections = { ...rowSelections };
		updateSelectAllState();
		checkboxKey += 1;
	}
	function isValidDate(date: Date | null): boolean {
		return date instanceof Date && !isNaN(date.getTime());
	}

	function getSelectedRows() {
		return Object.entries(rowSelections)
			.filter(([_, selected]) => selected)
			.map(([index, _]) => parseInt(index));
	}

	function formatDateForInput(date: Date | null): string {
		if (!date || !isValidDate(date) || isNaN(date.getTime())) return '';
		const y = date.getFullYear();
		const m = String(date.getMonth() + 1).padStart(2, '0');
		const d = String(date.getDate()).padStart(2, '0');
		return `${y}-${m}-${d}`;
	}

	function formatTimeForInput(date: Date | null): string {
		if (!date || !isValidDate(date) || isNaN(date.getTime())) return '';
		const hh = String(date.getHours()).padStart(2, '0');
		const mm = String(date.getMinutes()).padStart(2, '0');
		return `${hh}:${mm}`;
	}

	function setDateValue(node: HTMLInputElement, value: string) {
		node.value = value;
		return {
			update(newValue: string) {
				if (node.value !== newValue) {
					node.value = newValue;
				}
			}
		};
	}
	function updateDateFromString(row: any, index: number) {
		if (!row.dateString) return;

		let parsed: Date | null = null;
		if (row.dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
			parsed = new Date(row.dateString + 'T00:00:00');
		} else {
			parsed = parseDateDMY(row.dateString);
		}

		if (!parsed || isNaN(parsed.getTime())) return;
		row.date = parsed;
		if (row.scheduled_start_time instanceof Date) {
			const startTime = new Date(parsed);
			startTime.setHours(
				row.scheduled_start_time.getHours(),
				row.scheduled_start_time.getMinutes(),
				0,
				0
			);
			row.scheduled_start_time = startTime;
		}
		if (row.scheduled_end_time instanceof Date) {
			const endTime = new Date(parsed);
			endTime.setHours(
				row.scheduled_end_time.getHours(),
				row.scheduled_end_time.getMinutes(),
				0,
				0
			);
			row.scheduled_end_time = endTime;
		}
	}

	function updateStartTimeFromString(row: any, index: number) {
		if (!row.startTimeString) return;
		try {
			const [hours, minutes] = row.startTimeString.split(':').map((n) => parseInt(n));
			if (!isNaN(hours) && !isNaN(minutes)) {
				const baseDate = row.date instanceof Date ? new Date(row.date) : new Date();
				baseDate.setHours(hours, minutes, 0, 0);
				row.scheduled_start_time = baseDate;
			}
		} catch {}
	}

	function updateEndTimeFromString(row: any, index: number) {
		if (!row.endTimeString) {
			row.scheduled_end_time = null;
			return;
		}
		try {
			const [hours, minutes] = row.endTimeString.split(':').map((n) => parseInt(n));
			if (!isNaN(hours) && !isNaN(minutes)) {
				const baseDate = row.date instanceof Date ? new Date(row.date) : new Date();
				baseDate.setHours(hours, minutes, 0, 0);
				row.scheduled_end_time = baseDate;
			}
		} catch {}
	}

	function confirmUpload() {
		const selectedIndices = getSelectedRows();
		const selectedCount = selectedIndices.length;

		if (selectedCount === 0) {
			toast.error(UPLOAD_MESSAGES.SELECT_ROWS_TO_UPLOAD);
			return;
		}

		const validatedRows = validateSelectedRows();
		const rowsWithErrors = getRowsWithErrors(validatedRows);

		if (rowsWithErrors.length > 0) {
			const missingFields = getMissingFieldsSummary(rowsWithErrors);
			const errorMessage = formatMissingFieldsMessage(missingFields);

			toast.error(errorMessage);
			return;
		}

		showConfirmModal = true;
	}

	function cancelUpload() {
		showConfirmModal = false;
	}

	async function uploadSchedule(event) {
		if (event) event.preventDefault();
		const selectedIndices = getSelectedRows();
		if (selectedIndices.length === 0) {
			toast.error('No rows selected for upload');
			return;
		}

		showConfirmModal = false;
		isUploading = true;
		lastError = '';
		const keycloak = get(keycloakClient);
		const token = keycloak?.token;

		if (!token) {
			toast.error('Not authenticated');
			isUploading = false;
			return;
		}

		let successCount = 0;
		let failCount = 0;

		const rowsToUpload = selectedIndices.map((index) => validRows[index]);
		const totalRows = rowsToUpload.length;

		const rowsWithErrors = rowsToUpload.filter((row) => hasValidationErrors(row));
		if (rowsWithErrors.length > 0) {
			toast.error(
				'Cannot upload: Some selected rows have validation errors. Please fix them first.'
			);
			isUploading = false;
			return;
		}

		for (let i = 0; i < rowsToUpload.length; i++) {
			const row = rowsToUpload[i];
			try {
				let departureTime = row.scheduled_start_time;
				let arrivalTime = row.scheduled_end_time || null;

				if (!(departureTime instanceof Date)) {
					departureTime = new Date(departureTime);
				}

				if (arrivalTime && !(arrivalTime instanceof Date)) {
					arrivalTime = new Date(arrivalTime);
				}

				if (isNaN(departureTime.getTime())) {
					throw new Error('Invalid departure time');
				}

				if (arrivalTime && isNaN(arrivalTime.getTime())) {
					arrivalTime = null;
				}

				const departureTimeUTC = new Date(departureTime.toISOString());
				const arrivalTimeUTC = arrivalTime ? new Date(arrivalTime.toISOString()) : null;

				const pilotId = row.pilot_id;
				const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

				if (!pilotId || !uuidRegex.test(pilotId)) {
					throw new Error(`Please select a valid Pilot for row ${i + 1}`);
				}

				const pilotExists = pilots.some((p) => p.id === pilotId);
				if (!pilotExists) {
					throw new Error(
						`Selected Pilot ID ${pilotId} does not exist in the system. Please select a valid pilot.`
					);
				}

				let fromLocationId = String(row.start_location_id || '');
				let toLocationId = String(row.end_location_id || '');
				let gliderId = String(row.glider_id || '');

				let matchedRoute = null;
				if (row.route_id && routes && routes.length > 0) {
					matchedRoute = routes.find((r) => Number(r.id) === Number(row.route_id)) || null;
				}
				if (!matchedRoute) {
					const key = `${String(row.start_location_name || '')
						.toLowerCase()
						.trim()}|${String(row.end_location_name || '')
						.toLowerCase()
						.trim()}`;
					if (routeMap && routeMap.size > 0 && key.includes('|')) {
						matchedRoute = routeMap.get(key) || null;
					}
				}
				if (!matchedRoute && fromLocationId && toLocationId && routes && routes.length > 0) {
					const fromIdNum = Number(fromLocationId);
					const toIdNum = Number(toLocationId);
					if (!isNaN(fromIdNum) && !isNaN(toIdNum)) {
						matchedRoute =
							routes.find(
								(r) =>
									Number(r.start_location_id) === fromIdNum && Number(r.end_location_id) === toIdNum
							) || null;
					}
				}
				if (matchedRoute) {
					fromLocationId = String(matchedRoute.start_location_id);
					toLocationId = String(matchedRoute.end_location_id);
				}
				if (!matchedRoute) {
					throw new Error(
						`Route not found in rides for "${row.start_location_name}" → "${row.end_location_name}" (row ${i + 1}).`
					);
				}
				if (!gliderId || !glidersMapById.has(gliderId))
					throw new Error(`Invalid glider ID for row ${i + 1}`);

				if (fromLocationId === toLocationId && locationsMapById.size > 1) {
					for (const [id] of locationsMapById.entries()) {
						if (id !== fromLocationId) {
							toLocationId = id;
							break;
						}
					}
				}

				const gliderName = glidersMapById.get(gliderId)?.name || 'Unknown Glider';

				if (rideStatuses.length === 0) {
					throw new Error(
						`No ride statuses available. Please ensure ride statuses are configured in the system.`
					);
				}
				const defaultStatusId = rideStatuses[0].id;

				const flightData: any = {
					from_location: Number(fromLocationId),
					to_location: Number(toLocationId),
					departure_time: departureTimeUTC.toISOString(),
					ride_status_id: Number(defaultStatusId),
					glider_id: Number(gliderId),
					glider_name: gliderName,
					operator_id: pilotId,
					has_package: false
				};

				if (arrivalTimeUTC) {
					flightData.arrival_time = arrivalTimeUTC.toISOString();
				} else {
					flightData.arrival_time = departureTimeUTC.toISOString();
				}
				flightData.package_description = String(row.package_description || '').trim();
				if (matchedRoute && matchedRoute.id) {
					flightData.route_id = Number(matchedRoute.id);

					if (row.cancel_reason_name && cancelReasons && cancelReasons.length > 0) {
						const name = String(row.cancel_reason_name).trim().toLowerCase();
						const found = cancelReasons.find(
							(c) =>
								String(c.name || '')
									.trim()
									.toLowerCase() === name
						);
						if (found) {
							flightData.cancel_reason_id = Number(found.id);
						}
					}
				} else if (row.route_id && !isNaN(Number(row.route_id))) {
					flightData.route_id = Number(row.route_id);
				}
				if (row.cancel_reason_id && !isNaN(Number(row.cancel_reason_id))) {
					flightData.cancel_reason_id = Number(row.cancel_reason_id);
				} else if (row.cancel_reason_name && cancelReasons && cancelReasons.length > 0) {
					const name = String(row.cancel_reason_name).trim().toLowerCase();
					const found = cancelReasons.find(
						(c) =>
							String(c.name || '')
								.trim()
								.toLowerCase() === name
					);
					flightData.cancel_reason_id = found ? Number(found.id) : null;
				} else {
					flightData.cancel_reason_id = null;
				}

				try {
					await createRide(flightData);
					successCount++;
					toast.success(`Uploaded flight ${i + 1}/${totalRows}`);
				} catch (error) {
					failCount++;
					console.error('API Error:', error);
					let errorMessage = error.message || 'Unknown error';

					if (error.message && error.message.includes('Failed to fetch')) {
						errorMessage =
							'Network error: cannot connect to server. Check your internet connection or server availability.';
					} else if (error.response) {
						errorMessage = `Server error (${error.response.status}): ${error.response.data?.message || errorMessage}`;
						console.error('Error response:', error.response);
					}

					lastError = errorMessage;
					toast.error(`Error processing flight ${i + 1}/${totalRows}: ${errorMessage}`, {
						duration: 5000
					});
				}
			} catch (error) {
				failCount++;
				console.error('General error:', error);
				let errorMessage = error.message || 'Unknown error';
				lastError = errorMessage;
				toast.error(`Error processing flight ${i + 1}/${totalRows}: ${errorMessage}`, {
					duration: 5000
				});
			}
		}

		isUploading = false;
		uploadStats = {
			total: totalRows,
			success: successCount,
			failed: failCount
		};

		if (failCount === 0) {
			uploadSuccess = true;
			toast.success(`Successfully uploaded ${successCount} flights`);
		} else {
			toast.error(`Uploaded ${successCount} flights, with errors: ${failCount}`);
		}
	}

	function resetAfterUpload() {
		uploadSuccess = false;

		if (uploadStats.success > 0) {
			const selectedIndices = getSelectedRows();
			selectedIndices.forEach((index) => {
				rowSelections[index] = false;
			});
			rowSelections = { ...rowSelections };
			updateSelectAllState();
		}
	}
</script>

<div class="container mx-auto w-full max-w-full px-2 py-6">
	<h1 class="mb-6 text-2xl font-bold">Upload Flight Schedule</h1>

	{#if lastError}
		<div class="mb-6 rounded border-l-4 border-red-500 bg-red-50 px-4 py-3 text-red-700 shadow-md">
			<div class="flex items-start justify-between">
				<div>
					<h2 class="mb-2 font-bold">Upload Error</h2>
					<p>{lastError}</p>
				</div>
				<button
					class="text-xl font-bold text-red-500 hover:text-red-700"
					on:click={() => (lastError = '')}
				>
					×
				</button>
			</div>
			<button
				class="btn btn-secondary btn-sm mt-3"
				on:click={() => (showErrorDetails = !showErrorDetails)}
			>
				{showErrorDetails ? 'Hide details' : 'Show details'}
			</button>
			{#if showErrorDetails}
				<div class="mt-2 rounded bg-red-100 p-2 text-sm">
					<p class="mb-1">If you see "Failed to fetch" error:</p>
					<ul class="list-disc pl-5">
						<li>Check your internet connection</li>
						<li>Check VPN if using</li>
						<li>API server might be unavailable - contact administrator</li>
					</ul>
				</div>
			{/if}
		</div>
	{/if}

	{#if isApiError}
		<div class="mb-6 rounded border border-red-200 bg-red-50 px-4 py-3 text-red-700">
			<h2 class="mb-2 font-bold">API Connection Error</h2>
			<p>
				Unable to fetch reference data needed for validation. Please check network connection and
				try again.
			</p>
			{#if apiErrorDetails}
				<div class="mt-2 rounded bg-red-100 p-2 text-sm">
					<strong>Error details:</strong>
					{apiErrorDetails}
				</div>
			{/if}
			<button class="btn btn-primary btn-md mt-4" on:click={() => window.location.reload()}>
				Reload Page
			</button>
		</div>
	{/if}
	{#if uploadSuccess}
		<div class="mb-6 rounded border border-green-200 bg-green-50 px-4 py-3 text-green-700">
			<h2 class="mb-2 font-bold">Upload Successful</h2>
			<p>Successfully uploaded {uploadStats.success} flights to the system.</p>

			<button class="btn btn-primary btn-md mt-4" on:click={resetAfterUpload}>
				Upload Another File
			</button>
		</div>
	{:else}
		<div class="mb-6 rounded-lg bg-white p-6 shadow-md">
			<h2 class="mb-4 text-xl font-semibold">1. Upload Excel File</h2>

			{#if files.length === 0}
				<Dropzone
					accept=".xlsx,.xls"
					on:drop={handleFilesSelect}
					class="cursor-pointer rounded-lg border-2 border-dashed border-gray-300 p-10 text-center hover:bg-gray-50"
				>
					<p class="text-gray-500">Drag and drop Excel file here or click to select</p>
				</Dropzone>
			{:else}
				<div
					class="flex items-center justify-between rounded-lg border border-green-200 bg-green-50 p-4"
				>
					<div>
						<p class="font-medium">{files[0].name}</p>
						<p class="text-sm text-gray-500">{(files[0].size / 1024).toFixed(2)} KB</p>
					</div>
					<button
						class="btn btn-text text-red-500 hover:text-red-700"
						on:click={() => {
							files = [];
							workbook = null;
							sheetNames = [];
							selectedSheet = '';
							parsedData = [];
							validRows = [];
							didInitialIdMapping = false;
							rowSelections = {};
							selectAll = false;
						}}
					>
						Remove
					</button>
				</div>
			{/if}
		</div>

		{#if sheetNames.length > 0}
			<div class="mb-6 rounded-lg bg-white p-6 shadow-md">
				<h2 class="mb-4 text-xl font-semibold">2. Select Sheet to Analyze</h2>

				<div class="mb-4">
					<label for="sheetSelect" class="mb-1 block text-sm font-medium text-gray-700"
						>Select sheet:</label
					>
					<select
						id="sheetSelect"
						bind:value={selectedSheet}
						on:change={parseSheet}
						class="focus:ring-primary focus:border-primary mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:outline-none sm:text-sm"
					>
						{#each sheetNames as name}
							<option value={name}>{name}</option>
						{/each}
					</select>
				</div>
			</div>
		{/if}

		{#if isLoading || isValidating}
			<div class="my-8 flex justify-center">
				<div class="spinner-container-center">
					<div class="common-spinner spinner-lg"></div>
					<span class="ml-2 text-gray-500"
						>{isLoading ? 'Reading file...' : 'Validating data...'}</span
					>
				</div>
			</div>
		{:else if validRows.length > 0}
			<div class="mb-6 rounded-lg bg-white p-4 shadow-md">
				<h2 class="mb-4 text-xl font-semibold">3. Verify and Confirm Data</h2>

				<div class="mb-4 flex items-center justify-between">
					<p>Found {validRows.length} rows ready for upload out of {parsedData.length} total.</p>

					<div class="flex items-center">
						<label class="mr-4 inline-flex items-center">
							{#key checkboxKey}
								<input
									type="checkbox"
									bind:checked={selectAll}
									on:change={handleSelectAllChange}
									class="text-primary focus:border-primary focus:ring-primary rounded border-gray-300 focus:ring focus:ring-opacity-50"
								/>
							{/key}
							<span class="ml-2 text-sm">Select All</span>
						</label>

						<span class="text-sm text-gray-600"
							>{getSelectedCount()} of {validRows.length} selected</span
						>
					</div>
				</div>
				<div class="w-full overflow-x-auto">
					<table class="min-w-full table-fixed divide-y divide-gray-200">
						<thead class="bg-gray-50">
							<tr>
								<th
									scope="col"
									class="w-10 px-2 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
								>
									Select
								</th>
								<th
									scope="col"
									class="w-8 px-2 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
								>
									#
								</th>
								<th
									scope="col"
									class="w-28 px-2 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
									>Glider</th
								>
								<th
									scope="col"
									class="w-32 px-2 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
									>From</th
								>
								<th
									scope="col"
									class="w-32 px-2 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
									>To</th
								>
								<th
									scope="col"
									class="w-48 px-2 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
									>Pilot</th
								>
								<th
									scope="col"
									class="w-24 px-2 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
									>Date</th
								>
								<th
									scope="col"
									class="w-20 px-2 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
									>Departure ({timezoneDisplayName})</th
								>
								<th
									scope="col"
									class="w-20 px-2 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
									>Arrival ({timezoneDisplayName})</th
								>
								<th
									scope="col"
									class="w-24 px-2 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
									>Notes</th
								>
							</tr>
						</thead>
						<tbody class="divide-y divide-gray-200 bg-white">
							{#each validRows as row, i (i)}
								<tr
									class={i % 2 === 0 ? 'bg-white' : 'bg-gray-50'}
									class:bg-blue-50={isRowSelected(i)}
								>
									<td class="whitespace-nowrap px-2 py-2">
										{#key `${checkboxKey}-${i}`}
											<input
												type="checkbox"
												bind:checked={rowSelections[i]}
												on:change={() => handleRowSelectionChange(i)}
												class="text-primary focus:border-primary focus:ring-primary cursor-pointer rounded border-gray-300 focus:ring focus:ring-opacity-50"
												aria-label={ARIA_LABELS.ROW_CHECKBOX(i + 1)}
											/>
										{/key}
									</td>
									<td class="whitespace-nowrap px-2 py-2 text-sm text-gray-500">
										{row.sort || i + 1}
									</td>
									<td class="whitespace-nowrap px-2 py-2 text-sm font-medium text-gray-900">
										<select
											bind:value={row.glider_id}
											class="focus:ring-primary focus:border-primary block w-full rounded border px-1 py-1 text-sm {hasFieldError(
												row,
												'glider'
											)
												? 'border-red-500 bg-red-50'
												: 'border-gray-300'}"
											on:change={(e) => {
												row.glider_id = String(e.target.value);
												const selectedGlider = gliders.find(
													(g) => String(g.id) === String(e.target.value)
												);
												if (selectedGlider) {
													row.glider_name = selectedGlider.name;
												}
												validateSingleRow(i);
											}}
											aria-label={ARIA_LABELS.GLIDER_SELECT}
											aria-invalid={hasFieldError(row, 'glider')}
											aria-describedby={hasFieldError(row, 'glider')
												? `glider-error-${i}`
												: undefined}
										>
											<option value="">Select Glider</option>
											{#each gliders as glider}
												<option value={String(glider.id)}>{glider.name}</option>
											{/each}
										</select>
									</td>
									<td class="whitespace-nowrap px-2 py-2 text-sm text-gray-900">
										<select
											bind:value={row.start_location_id}
											class="focus:ring-primary focus:border-primary block w-full rounded border px-1 py-1 text-sm {hasFieldError(
												row,
												'start_location'
											)
												? 'border-red-500 bg-red-50'
												: 'border-gray-300'}"
											aria-label={ARIA_LABELS.START_LOCATION_SELECT}
											aria-invalid={hasFieldError(row, 'start_location')}
											aria-describedby={hasFieldError(row, 'start_location')
												? `start-location-error-${i}`
												: undefined}
											on:change={(e) => {
												row.start_location_id = String(e.target.value);
												const selectedLocation = locations.find(
													(l) => String(l.id) === String(e.target.value)
												);
												if (selectedLocation) {
													row.start_location_name = selectedLocation.name;
												}
												validateSingleRow(i);
											}}
										>
											<option value="">Select Location</option>
											{#each locations as location}
												<option value={String(location.id)}>{location.name}</option>
											{/each}
										</select>
									</td>
									<td class="whitespace-nowrap px-2 py-2 text-sm text-gray-900">
										<select
											bind:value={row.end_location_id}
											class="focus:ring-primary focus:border-primary block w-full rounded border px-1 py-1 text-sm {hasFieldError(
												row,
												'end_location'
											)
												? 'border-red-500 bg-red-50'
												: 'border-gray-300'}"
											aria-label={ARIA_LABELS.END_LOCATION_SELECT}
											aria-invalid={hasFieldError(row, 'end_location')}
											aria-describedby={hasFieldError(row, 'end_location')
												? `end-location-error-${i}`
												: undefined}
											on:change={(e) => {
												row.end_location_id = String(e.target.value);
												const selectedLocation = locations.find(
													(l) => String(l.id) === String(e.target.value)
												);
												if (selectedLocation) {
													row.end_location_name = selectedLocation.name;
													console.log(
														`Updated end_location_name to: "${selectedLocation.name}" for row ${i}`
													);
												}
												validateSingleRow(i);
											}}
										>
											<option value="">Select Location</option>
											{#each locations as location}
												<option value={String(location.id)}>{location.name}</option>
											{/each}
										</select>
									</td>
									<td class="whitespace-nowrap px-2 py-2 text-sm text-gray-900">
										<select
											bind:value={row.pilot_id}
											class="focus:ring-primary focus:border-primary block w-full rounded border px-1 py-1 text-sm {hasFieldError(
												row,
												'pilot'
											)
												? 'border-red-500 bg-red-50'
												: 'border-gray-300'}"
											on:change={() => validateSingleRow(i)}
											aria-label={ARIA_LABELS.PILOT_SELECT}
											aria-invalid={hasFieldError(row, 'pilot')}
											aria-describedby={hasFieldError(row, 'pilot')
												? `pilot-error-${i}`
												: undefined}
										>
											<option value="">Select Pilot</option>
											{#each pilots as pilot}
												<option value={String(pilot.id)}
													>{pilot.email || `${pilot.firstName} ${pilot.lastName}`}</option
												>
											{/each}
										</select>
									</td>
									<td
										class="whitespace-nowrap px-2 py-2 text-sm text-gray-900 {hasFieldError(
											row,
											'date'
										)
											? 'border-l-2 border-red-500 bg-red-50'
											: ''}"
									>
										<input
											type="date"
											class="w-full rounded border px-1 py-1 text-xs {hasFieldError(row, 'date')
												? 'border-red-500 bg-red-50'
												: 'border-gray-300'}"
											use:setDateValue={row.dateString || ''}
											on:input={(e) => {
												row.dateString = e.target.value;
												updateDateFromString(row, i);
												validateSingleRow(i);
											}}
										/>
									</td>
									<td
										class="whitespace-nowrap px-2 py-2 text-sm text-gray-900 {hasFieldError(
											row,
											'start_time'
										)
											? 'border-l-2 border-red-500 bg-red-50'
											: ''}"
									>
										<input
											type="text"
											bind:value={row.startTimeString}
											placeholder="HH:MM"
											pattern="^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
											class="w-full rounded border px-1 py-1 text-xs {hasFieldError(
												row,
												'start_time'
											)
												? 'border-red-500 bg-red-50'
												: 'border-gray-300'}"
											on:input={(e) => {
												let value = e.target.value.replace(/[^\d]/g, '');
												if (value.length >= 2) {
													value = value.slice(0, 2) + ':' + value.slice(2, 4);
												}
												row.startTimeString = value;
												validRows = [...validRows];
											}}
											on:change={() => {
												updateStartTimeFromString(row, i);
												validateSingleRow(i);
											}}
										/>
									</td>
									<td
										class="whitespace-nowrap px-2 py-2 text-sm text-gray-900 {hasFieldError(
											row,
											'end_time'
										)
											? 'border-l-2 border-red-500 bg-red-50'
											: ''}"
									>
										<input
											type="text"
											bind:value={row.endTimeString}
											placeholder="HH:MM"
											pattern="^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
											class="w-full rounded border px-1 py-1 text-xs {hasFieldError(row, 'end_time')
												? 'border-red-500 bg-red-50'
												: 'border-gray-300'}"
											on:input={(e) => {
												let value = e.target.value.replace(/[^\d]/g, '');
												if (value.length >= 2) {
													value = value.slice(0, 2) + ':' + value.slice(2, 4);
												}
												row.endTimeString = value;
												validRows = [...validRows];
											}}
											on:change={() => {
												updateEndTimeFromString(row, i);
												validateSingleRow(i);
											}}
										/>
									</td>
									<td class="whitespace-nowrap px-2 py-2 text-sm text-gray-900">
										{row.notes || '-'}
									</td>
								</tr>
							{/each}
						</tbody>
					</table>
				</div>
				{#if getSelectedRows().length > 0 && getSelectedRows().some( (index) => hasValidationErrors(validRows[index]) )}
					<div class="mt-4 rounded-lg border border-red-200 bg-red-50 p-4">
						<h3 class="mb-2 font-semibold text-red-800">Validation Errors in Selected Rows</h3>
						<p class="mb-2 text-sm text-red-700">
							The following selected rows have missing or invalid required fields. Please fix them
							before uploading:
						</p>
						<div class="max-h-32 overflow-y-auto">
							{#each getSelectedRows() as selectedIndex}
								{@const row = validRows[selectedIndex]}
								{#if hasValidationErrors(row)}
									<div class="mb-1 text-sm text-red-600">
										<strong>Row {selectedIndex + 1}:</strong>
										{row._validationErrors.join(', ')}
									</div>
								{/if}
							{/each}
						</div>
					</div>
				{/if}

				<div class="mt-6 flex flex-col items-center justify-between sm:flex-row">
					{#if selectedCount === 0}
						<p class="mb-2 text-red-500 sm:mb-0">Select rows to upload</p>
					{:else}
						<div class="mb-2 flex items-center sm:mb-0">
							<p class="mr-3 text-green-600">
								Selected {selectedCount} of {validRows.length} rows
							</p>
							<button
								class="btn btn-secondary btn-sm ml-2"
								on:click={() => {
									rowSelections = {};
									updateSelectAllState();
								}}
							>
								Clear Selection
							</button>
						</div>
					{/if}

					<button
						class="btn btn-primary btn-md mt-2 sm:mt-0 {selectedCount === 0 ? 'opacity-60' : ''}"
						on:click={(e) => confirmUpload()}
						disabled={selectedCount === 0}
					>
						Upload Selected Rows
					</button>
				</div>
			</div>
		{:else if parsedData.length > 0}
			<div class="mb-6 rounded-lg bg-white p-6 shadow-md">
				<h2 class="mb-4 text-xl font-semibold">Validation Results</h2>

				<div class="mb-4 mt-6">
					<p class="mb-2 font-medium text-gray-700">All found rows ({parsedData.length}):</p>
					<div class="w-full overflow-x-auto rounded-lg border border-gray-200">
						<table class="min-w-full table-fixed text-sm">
							<thead>
								<tr class="bg-gray-100">
									<th class="w-8 border-b border-r border-gray-200 px-2 py-2 text-left">№</th>
									<th class="w-10 border-b border-r border-gray-200 px-2 py-2 text-left">Select</th>
									<th class="w-32 border-b border-r border-gray-200 px-2 py-2 text-left"
										>start_location</th
									>
									<th class="w-32 border-b border-r border-gray-200 px-2 py-2 text-left"
										>end_location</th
									>
									<th class="w-24 border-b border-r border-gray-200 px-2 py-2 text-left">date</th>
									<th class="w-20 border-b border-r border-gray-200 px-2 py-2 text-left">time</th>
									<th class="w-28 border-b border-r border-gray-200 px-2 py-2 text-left">glider</th>
									<th class="w-48 border-b border-r border-gray-200 px-2 py-2 text-left">pilot</th>
									<th class="w-24 border-b border-gray-200 px-2 py-2 text-left">notes</th>
								</tr>
							</thead>
							<tbody>
								{#each validRows as row, i (i)}
									<tr class={i % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
										<td class="border-r border-gray-200 px-2 py-2">{row.sort || i + 1}</td>
										<td class="border-r border-gray-200 px-2 py-2">
											{#key `${checkboxKey}-${i}-summary`}
												<input
													type="checkbox"
													bind:checked={rowSelections[i]}
													on:change={() => handleRowSelectionChange(i)}
													class="text-primary focus:border-primary focus:ring-primary cursor-pointer rounded border-gray-300 focus:ring focus:ring-opacity-50"
												/>
											{/key}
										</td>
										<td class="border-r border-gray-200 px-2 py-2"
											>{row.start_location_name || ''}</td
										>
										<td class="border-r border-gray-200 px-2 py-2">{row.end_location_name || ''}</td
										>
										<td class="border-r border-gray-200 px-2 py-2">
											{#if row.date}
												{typeof row.date === 'number'
													? excelDateToJSDate(row.date)?.toLocaleDateString()
													: new Date(row.date).toLocaleDateString()}
											{/if}
										</td>
										<td class="border-r border-gray-200 px-2 py-2">
											{#if row.scheduled_start_time}
												{typeof row.scheduled_start_time === 'number'
													? excelTimeToHHMM(row.scheduled_start_time)
													: row.scheduled_start_time instanceof Date
														? row.scheduled_start_time.toLocaleTimeString([], {
																hour: '2-digit',
																minute: '2-digit'
															})
														: row.scheduled_start_time}
											{/if}
										</td>
										<td class="border-r border-gray-200 px-2 py-2">{row.glider_name || ''}</td>
										<td class="border-r border-gray-200 px-2 py-2">
											{row.pilot_name || row.pilot_id || row.pilot_email || ''}
										</td>
										<td class="border-r border-gray-200 px-2 py-2">{row.notes || ''}</td>
									</tr>
								{/each}
							</tbody>
						</table>
					</div>
				</div>

				<div class="mt-4 flex flex-col items-center justify-between sm:flex-row">
					<div class="mb-4 sm:mb-0">
						<label class="mr-4 inline-flex items-center">
							{#key `bottom-${checkboxKey}`}
								<input
									type="checkbox"
									bind:checked={selectAll}
									on:change={handleSelectAllChange}
									class="text-primary focus:border-primary focus:ring-primary rounded border-gray-300 focus:ring focus:ring-opacity-50"
								/>
							{/key}
							<span class="ml-2 text-sm">Select All</span>
						</label>
						<span class="text-sm text-gray-600"
							>{getSelectedCount()} of {validRows.length} selected</span
						>

						<button
							class="btn btn-secondary btn-sm ml-3"
							on:click={() => {
								rowSelections = {};
								updateSelectAllState();
							}}
							disabled={getSelectedCount() === 0}
						>
							Clear Selection
						</button>
					</div>

					<button
						class="btn btn-primary btn-md"
						on:click={(e) => confirmUpload()}
						disabled={selectedCount === 0}
					>
						Upload Selected Rows
					</button>
				</div>

				<p class="mt-4 text-gray-700">
					For uploading, the file must contain the following columns (case insensitive):
				</p>
				<ul class="mb-4 mt-2 list-disc pl-5 text-gray-600">
					<li>glider_id or glider_name</li>
					<li>start_location_name (or simply "start_location")</li>
					<li>end_location_name (or simply "end_location")</li>
					<li>pilot_id or pilot_name (email addresses also supported)</li>
					<li>scheduled_start_time (or "date" column if time is included)</li>
					<li>scheduled_end_time (optional)</li>
					<li>notes, logs, or description (optional, used as package_description)</li>
				</ul>

				<div
					class="mt-4 rounded-lg border {timezoneDetectionFailed
						? 'border-yellow-200 bg-yellow-50'
						: 'border-blue-200 bg-blue-50'} p-4"
				>
					<h3
						class="mb-2 font-medium {timezoneDetectionFailed ? 'text-yellow-800' : 'text-blue-800'}"
					>
						Timezone Information:
					</h3>
					{#if timezoneDetectionFailed}
						<p class="text-yellow-700">
							{TIMEZONE_MESSAGES.TIMEZONE_DETECTION_FAILED}
						</p>
						{#if timezoneError}
							<p class="mt-1 text-sm text-yellow-600">Error: {timezoneError}</p>
						{/if}
					{:else}
						<p class="text-blue-700">
							{TIMEZONE_MESSAGES.TIMEZONE_INFO(userTimezone, timezoneDisplayName)}
						</p>
					{/if}
				</div>

				<div class="mt-6 rounded-lg border border-yellow-200 bg-yellow-50 p-4">
					<h3 class="mb-2 font-medium text-yellow-800">Diagnostic Information:</h3>
					<ul class="list-disc pl-5 text-yellow-700">
						<li>Gliders loaded: {gliders.length}</li>
						<li>Locations loaded: {locations.length}</li>
						<li>Pilots loaded: {pilots.length}</li>
						<li>Routes loaded: {routes.length}</li>
						<li>Rows parsed: {parsedData.length}</li>
						<li>
							Valid rows: {validRows.filter(
								(r) => !r._validationErrors || r._validationErrors.length === 0
							).length}
						</li>
					</ul>
					<p class="mt-2 text-sm text-yellow-700">
						For troubleshooting, open developer console (F12) to view detailed information.
					</p>
				</div>
			</div>
		{/if}
	{/if}
</div>

{#if showConfirmModal}
	<div class="fixed inset-0 z-50 flex items-center justify-center bg-gray-600 bg-opacity-50">
		<div class="w-full max-w-md rounded-lg bg-white p-6 shadow-xl">
			<h3 class="mb-4 text-lg font-bold">Confirm Upload</h3>
			<p class="mb-4">
				Are you sure you want to upload {getSelectedCount()} flights to the system?
			</p>

			<div class="flex justify-end space-x-3">
				<button class="btn btn-secondary btn-md" on:click={cancelUpload}> Cancel </button>
				<button class="btn btn-primary btn-md" on:click={(e) => uploadSchedule(e)}>
					Confirm Upload
				</button>
			</div>
		</div>
	</div>
{/if}

{#if isUploading}
	<div class="fixed inset-0 z-50 flex items-center justify-center bg-gray-600 bg-opacity-50">
		<div class="rounded-lg bg-white p-6 text-center shadow-xl">
			<div class="spinner-container-center">
				<div class="common-spinner spinner-lg"></div>
			</div>
			<h3 class="mb-2 text-lg font-bold">Uploading...</h3>
			<p>Please wait while your flights are being uploaded.</p>
		</div>
	</div>
{/if}

<style>
	.btn {
		@apply inline-flex cursor-pointer items-center justify-center border-0 text-center font-medium;
	}

	.btn-primary {
		@apply bg-primary-500 text-white transition-all duration-300;
	}

	.btn-primary:hover:not(:disabled) {
		@apply -translate-y-0.5 transform bg-primary-600 shadow-md;
	}

	.btn-primary:disabled {
		@apply cursor-not-allowed opacity-60;
	}

	.btn-secondary {
		@apply border border-gray-200 bg-gray-100 text-gray-600 transition-all duration-300;
	}

	.btn-secondary:hover:not(:disabled) {
		@apply bg-gray-200 text-gray-800;
	}

	.btn-text {
		@apply border-0 bg-transparent p-0 text-primary-500 transition-all duration-150;
	}

	.btn-text:hover {
		@apply text-primary-600 underline;
	}

	.btn-md {
		@apply gap-2 rounded-md px-4 py-2 text-base;
	}

	.btn-sm {
		@apply gap-1 rounded px-2 py-1 text-xs;
	}

	.common-spinner {
		@apply inline-block animate-spin rounded-full;
		border: 3px solid rgba(72, 168, 81, 0.3);
		border-top-color: theme('colors.primary.500');
	}

	.spinner-lg {
		@apply h-8 w-8;
	}

	.spinner-container-center {
		@apply flex items-center justify-center p-4;
	}
</style>
