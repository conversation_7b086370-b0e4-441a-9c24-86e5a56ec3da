<script lang="ts">
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import { Button, Label, Input, Textarea, Select, Checkbox } from 'flowbite-svelte';
	import { ArrowLeftOutline } from 'flowbite-svelte-icons';
	import { fetchRideById, updateRide, type Ride } from '$lib/services/rides.service';
	import { fetchRideStatuses, type RideStatus } from '$lib/services/ride-status.service';
	import { fetchCancelReasons, type CancelReason } from '$lib/services/cancel-reasons.service';
	import { fetchGliders, type Glider } from '$lib/services/gliders.service';
	import { fetchUsers, type UserData } from '$lib/services/users.service';
	import { fetchRoutes, type Route } from '$lib/services/routes.service';
	import { ensureUTC } from '$lib/utils/datetime';

	let rideInfo: Ride | null = null;
	let loading = true;
	let error: string | null = null;
	let rideId: number | null = null;

	let rideFormData = {
		ride_status_id: 0,
		has_package: false,
		package_description: '',
		departure_time: '',
		arrival_time: '',
		glider_id: 0,
		operator_id: '',
		route_id: 0,
		cancel_reason_id: 0
	};

	let operatorSearchTerm = '';
	let gliderSearchTerm = '';
	let showOperatorSuggestions = false;
	let showGliderSuggestions = false;
	let filteredOperators: UserData[] = [];
	let filteredGliders: Glider[] = [];

	let rideStatuses: RideStatus[] = [];
	let cancelReasons: CancelReason[] = [];
	let gliders: Glider[] = [];
	let operators: UserData[] = [];
	let routes: Route[] = [];

	let rideErrors: { [key: string]: string } = {};
	let saving = false;
	let showSuccessToast = false;

	onMount(async () => {
		try {
			loading = true;

			const rideIdParam = $page.url.searchParams.get('id');

			if (!rideIdParam) {
				error = 'No ride ID provided';
				return;
			}

			rideId = parseInt(rideIdParam);

			if (isNaN(rideId)) {
				error = 'Invalid ride ID!';
				return;
			}

			const [rideData, statusesData, cancelReasonsData, glidersData, operatorsData, routesData] =
				await Promise.all([
					fetchRideById(rideId),
					fetchRideStatuses().catch(() => []),
					fetchCancelReasons().catch(() => []),
					fetchGliders().catch(() => []),
					fetchUsers().catch(() => []),
					fetchRoutes().catch(() => [])
				]);

			rideInfo = rideData;
			rideStatuses = statusesData;
			cancelReasons = cancelReasonsData;
			gliders = glidersData;
			operators = operatorsData;
			routes = routesData;

			if (!rideInfo) {
				error = 'Ride not found';
				return;
			}

			rideFormData = {
				ride_status_id: rideInfo.ride_status_id || 0,
				has_package: rideInfo.has_package || false,
				package_description: rideInfo.package_description || '',
				departure_time: rideInfo.departure_time
					? new Date(ensureUTC(rideInfo.departure_time)).toISOString().slice(0, 16)
					: '',
				arrival_time: rideInfo.arrival_time
					? new Date(ensureUTC(rideInfo.arrival_time)).toISOString().slice(0, 16)
					: '',
				glider_id: rideInfo.glider_id || 0,
				operator_id: rideInfo.operator_id || '',
				route_id: rideInfo.route_id || 0,
				cancel_reason_id: rideInfo.cancel_reason_id || 0
			};

			operatorSearchTerm = getOperatorEmail(rideInfo.operator_id || '');
			gliderSearchTerm = getGliderName(rideInfo.glider_id || 0);
		} catch (err) {
			error = err instanceof Error ? err.message : 'Unknown error loading data';
		} finally {
			loading = false;
		}
	});

	function validateForm(): boolean {
		rideErrors = {};

		if (!rideFormData.departure_time) {
			rideErrors.departure_time = 'Departure time is required';
		}

		if (!rideFormData.arrival_time) {
			rideErrors.arrival_time = 'Arrival time is required';
		}

		if (rideFormData.departure_time && rideFormData.arrival_time) {
			const departureDate = new Date(rideFormData.departure_time);
			const arrivalDate = new Date(rideFormData.arrival_time);

			if (arrivalDate <= departureDate) {
				rideErrors.arrival_time = 'Arrival time must be after departure time';
			}
		}

		if (rideFormData.has_package && !rideFormData.package_description.trim()) {
			rideErrors.package_description = 'Package description is required when package is selected';
		}

		return Object.keys(rideErrors).length === 0;
	}

	const handleSubmit = async () => {
		if (!rideId || !validateForm()) return;

		try {
			saving = true;

			const departureTimeISO = new Date(rideFormData.departure_time).toISOString();
			const arrivalTimeISO = new Date(rideFormData.arrival_time).toISOString();

			const updateData = {
				ride_status_id: rideFormData.ride_status_id || undefined,
				has_package: rideFormData.has_package,
				package_description: rideFormData.package_description || undefined,
				departure_time: departureTimeISO,
				arrival_time: arrivalTimeISO,
				glider_id: rideFormData.glider_id || undefined,
				operator_id: rideFormData.operator_id || undefined,
				glider_name: gliderSearchTerm || undefined,
				route_id: rideFormData.route_id || undefined,
				cancel_reason_id: rideFormData.cancel_reason_id || undefined
			};

			const result = await updateRide(rideId, updateData);

			if (result) {
				showSuccessToast = true;
				rideInfo = await fetchRideById(rideId);
			} else {
				rideErrors.general = 'Failed to update ride. Please try again.';
			}
		} catch (err) {
			rideErrors.general = err instanceof Error ? err.message : 'Unknown error updating ride';
		} finally {
			saving = false;
		}
	};

	const handleCancel = () => {
		goto('/rides');
	};

	const hasError = (field: string) => {
		return rideErrors[field] !== undefined;
	};

	function getStatusName(statusId: number): string {
		const status = rideStatuses.find((s) => s.id === statusId);
		return status?.name || 'Unknown';
	}

	function getOperatorEmail(operatorId: string): string {
		const operator = operators.find((o) => o.id === operatorId);
		return operator?.email || 'Unknown';
	}

	function getGliderName(gliderId: number): string {
		const glider = gliders.find((g) => g.id === gliderId);
		return glider?.name || 'Unknown';
	}

	function getRouteName(routeId: number): string {
		const route = routes.find((r) => r.id === routeId);
		return route ? `${route.start_location_name} → ${route.end_location_name}` : 'Unknown';
	}

	function filterOperators(searchTerm: string) {
		if (!searchTerm.trim()) {
			filteredOperators = [];
			return;
		}

		const term = searchTerm.toLowerCase();
		filteredOperators = operators
			.filter(
				(operator) =>
					operator.email?.toLowerCase().includes(term) ||
					operator.firstName?.toLowerCase().includes(term) ||
					operator.lastName?.toLowerCase().includes(term) ||
					operator.username?.toLowerCase().includes(term)
			)
			.slice(0, 10);
	}

	function handleOperatorInput() {
		filterOperators(operatorSearchTerm);
		showOperatorSuggestions = filteredOperators.length > 0;
	}

	function selectOperator(operator: UserData) {
		operatorSearchTerm = operator.email || '';
		rideFormData.operator_id = operator.id;
		showOperatorSuggestions = false;
	}

	function filterGliders(searchTerm: string) {
		if (!searchTerm.trim()) {
			filteredGliders = [];
			return;
		}

		const term = searchTerm.toLowerCase();
		filteredGliders = gliders
			.filter(
				(glider) =>
					glider.name?.toLowerCase().includes(term) ||
					glider.registrationCode?.toLowerCase().includes(term) ||
					glider.line?.toLowerCase().includes(term)
			)
			.slice(0, 10);
	}

	function handleGliderInput() {
		filterGliders(gliderSearchTerm);
		showGliderSuggestions = filteredGliders.length > 0;
	}

	function selectGlider(glider: Glider) {
		gliderSearchTerm = glider.name || '';
		rideFormData.glider_id = glider.id;
		showGliderSuggestions = false;
	}
</script>

<div class="container mx-auto max-w-full px-4 py-6">
	<div class="mb-6">
		<Button color="light" class="flex items-center gap-2" on:click={handleCancel}>
			<ArrowLeftOutline class="h-4 w-4" />
			Back to Rides
		</Button>
	</div>

	<div class="rounded-lg bg-white p-6 shadow-md">
		<h1 class="mb-6 border-b pb-3 text-2xl font-bold text-gray-800">Edit Ride</h1>

		{#if loading}
			<div class="flex items-center justify-center py-12">
				<div
					class="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-primary-500 border-r-transparent align-[-0.125em]"
				></div>
				<span class="ml-3">Loading ride data...</span>
			</div>
		{:else if error}
			<div
				class="mb-4 rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700"
				role="alert"
			>
				<p>{error}</p>
				<button class="mt-2 text-blue-500 hover:underline" on:click={handleCancel}
					>Return to rides</button
				>
			</div>
		{:else}
			<!-- Current Ride Information -->
			<div class="mb-6 rounded-lg bg-gray-50 p-4">
				<h3 class="mb-2 text-lg font-semibold text-gray-800">Current Ride Information</h3>
				<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
					<div>
						<span class="text-sm font-medium text-gray-600">Ride ID:</span>
						<span class="ml-2 text-gray-800">{rideInfo?.id || 'N/A'}</span>
					</div>
					<div>
						<span class="text-sm font-medium text-gray-600">Current Status:</span>
						<span class="ml-2 text-gray-800">{getStatusName(rideInfo?.ride_status_id || 0)}</span>
					</div>
					<div>
						<span class="text-sm font-medium text-gray-600">Current Operator:</span>
						<span class="ml-2 text-gray-800">{getOperatorEmail(rideInfo?.operator_id || '')}</span>
					</div>
					<div>
						<span class="text-sm font-medium text-gray-600">Current Glider:</span>
						<span class="ml-2 text-gray-800">{getGliderName(rideInfo?.glider_id || 0)}</span>
					</div>
					<div class="md:col-span-2">
						<span class="text-sm font-medium text-gray-600">Route:</span>
						<span class="ml-2 text-gray-800">{getRouteName(rideInfo?.route_id || 0)}</span>
					</div>
				</div>
			</div>

			<!-- Edit Form -->
			<div class="rounded-lg bg-blue-50 p-6">
				<h2 class="mb-4 text-xl font-semibold text-gray-800">Edit Ride Details</h2>

				{#if rideErrors.general}
					<div class="mb-4 rounded border border-red-400 bg-red-100 p-3 text-red-700">
						{rideErrors.general}
					</div>
				{/if}

				{#if showSuccessToast}
					<div class="mb-4 rounded border border-green-400 bg-green-100 p-3 text-green-700">
						Ride updated successfully!
					</div>
				{/if}

				<form on:submit|preventDefault={handleSubmit} class="space-y-6">
					<div class="grid grid-cols-1 gap-6 md:grid-cols-2">
						<!-- Departure Time -->
						<div>
							<Label for="departure_time" class="mb-2">Departure Time *</Label>
							<Input
								id="departure_time"
								type="datetime-local"
								bind:value={rideFormData.departure_time}
								required
								class={hasError('departure_time') ? 'border-red-500' : ''}
							/>
							{#if hasError('departure_time')}
								<p class="mt-1 text-sm text-red-600">{rideErrors.departure_time}</p>
							{/if}
						</div>

						<!-- Arrival Time -->
						<div>
							<Label for="arrival_time" class="mb-2">Arrival Time *</Label>
							<Input
								id="arrival_time"
								type="datetime-local"
								bind:value={rideFormData.arrival_time}
								required
								class={hasError('arrival_time') ? 'border-red-500' : ''}
							/>
							{#if hasError('arrival_time')}
								<p class="mt-1 text-sm text-red-600">{rideErrors.arrival_time}</p>
							{/if}
						</div>

						<!-- Ride Status -->
						<div>
							<Label for="ride_status_id" class="mb-2">Ride Status</Label>
							<Select id="ride_status_id" bind:value={rideFormData.ride_status_id}>
								<option value={0}>Select status...</option>
								{#each rideStatuses as status}
									<option value={status.id}>{status.name}</option>
								{/each}
							</Select>
						</div>

						<!-- Route -->
						<div>
							<Label for="route_id" class="mb-2">Route</Label>
							<Select id="route_id" bind:value={rideFormData.route_id}>
								<option value={0}>Select route...</option>
								{#each routes as route}
									<option value={route.id}
										>{route.start_location_name} → {route.end_location_name}</option
									>
								{/each}
							</Select>
						</div>

						<!-- Operator -->
						<div class="relative">
							<Label for="operator_search" class="mb-2">Operator Email</Label>
							<Input
								id="operator_search"
								bind:value={operatorSearchTerm}
								on:input={handleOperatorInput}
								on:focus={handleOperatorInput}
								placeholder="Start typing operator email..."
								autocomplete="off"
							/>

							{#if showOperatorSuggestions && filteredOperators.length > 0}
								<div class="suggestions-dropdown">
									{#each filteredOperators as operator}
										<button
											type="button"
											class="suggestion-item"
											on:click={() => selectOperator(operator)}
										>
											<div class="suggestion-primary">{operator.email}</div>
											<div class="suggestion-secondary">
												{operator.firstName}
												{operator.lastName}
											</div>
										</button>
									{/each}
								</div>
							{/if}
						</div>

						<!-- Glider -->
						<div class="relative">
							<Label for="glider_search" class="mb-2">Glider Name</Label>
							<Input
								id="glider_search"
								bind:value={gliderSearchTerm}
								on:input={handleGliderInput}
								on:focus={handleGliderInput}
								placeholder="Start typing glider name..."
								autocomplete="off"
							/>

							{#if showGliderSuggestions && filteredGliders.length > 0}
								<div class="suggestions-dropdown">
									{#each filteredGliders as glider}
										<button
											type="button"
											class="suggestion-item"
											on:click={() => selectGlider(glider)}
										>
											<div class="suggestion-primary">{glider.name}</div>
											<div class="suggestion-secondary">
												ID: {glider.id}
											</div>
										</button>
									{/each}
								</div>
							{/if}
						</div>

						<!-- Cancel Reason -->
						<div>
							<Label for="cancel_reason_id" class="mb-2">Cancel Reason</Label>
							<Select id="cancel_reason_id" bind:value={rideFormData.cancel_reason_id}>
								<option value={0}>No cancellation</option>
								{#each cancelReasons as reason}
									<option value={reason.id}>{reason.name}</option>
								{/each}
							</Select>
						</div>
					</div>

					<!-- Package Information -->
					<fieldset class="mt-8 border-t border-gray-200 pt-6">
						<legend class="-ml-2 px-2 text-lg font-medium text-gray-800">Package Information</legend
						>

						<div class="mt-4">
							<div class="mb-4 flex items-center">
								<Checkbox id="has_package" bind:checked={rideFormData.has_package} class="mr-2" />
								<Label for="has_package">This ride carries a package</Label>
							</div>

							{#if rideFormData.has_package}
								<div>
									<Label for="package_description" class="mb-2">Package Description *</Label>
									<Textarea
										id="package_description"
										bind:value={rideFormData.package_description}
										placeholder="Describe the package contents and any special handling requirements"
										rows="3"
										class={hasError('package_description') ? 'border-red-500' : ''}
									/>
									{#if hasError('package_description')}
										<p class="mt-1 text-sm text-red-600">{rideErrors.package_description}</p>
									{/if}
								</div>
							{/if}
						</div>
					</fieldset>

					<!-- Submit Button -->
					<div class="flex justify-end space-x-4 border-t border-gray-200 pt-6">
						<Button type="button" color="alternative" on:click={handleCancel}>Cancel</Button>
						<Button type="submit" color="primary" disabled={saving}>
							{#if saving}
								<div
									class="mr-2 inline-block h-4 w-4 animate-spin rounded-full border-2 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"
								></div>
							{/if}
							{saving ? 'Saving...' : 'Save Changes'}
						</Button>
					</div>
				</form>
			</div>
		{/if}
	</div>
</div>

<style>
	.suggestions-dropdown {
		position: absolute;
		top: 100%;
		left: 0;
		right: 0;
		z-index: 50;
		max-height: 200px;
		overflow-y: auto;
		background: white;
		border: 1px solid #e5e7eb;
		border-radius: 8px;
		box-shadow:
			0 10px 15px -3px rgba(0, 0, 0, 0.1),
			0 4px 6px -2px rgba(0, 0, 0, 0.05);
		margin-top: 4px;
	}

	.suggestion-item {
		display: block;
		width: 100%;
		padding: 12px 16px;
		text-align: left;
		background: none;
		border: none;
		cursor: pointer;
		transition: background-color 0.2s ease;
		border-bottom: 1px solid #f3f4f6;
	}

	.suggestion-item:hover {
		background-color: #f9fafb;
	}

	.suggestion-item:last-child {
		border-bottom: none;
	}

	.suggestion-primary {
		font-weight: 500;
		color: #374151;
		font-size: 14px;
	}

	.suggestion-secondary {
		font-size: 12px;
		color: #6b7280;
		margin-top: 2px;
	}
</style>
