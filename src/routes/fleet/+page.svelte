<script lang="ts">
	import { onMount } from 'svelte';
	import {
		Button,
		Input,
		Select,
		Table,
		TableBody,
		TableBodyCell,
		TableBodyRow,
		TableHead,
		TableHeadCell
	} from 'flowbite-svelte';
	import { SearchOutline } from 'flowbite-svelte-icons';
	import { environment } from '$lib/environment';
	import { fetchGliders, type Glider } from '$lib/services/gliders.service';
	import {
		getAvailableStatuses,
		getStatusColor,
		getTextColor,
		type GliderStatus
	} from '$lib/services/status.service';

	interface FleetItem {
		name: string;
		ip: string;
		status: string;
		gliderStatus: GliderStatus | null;
		region: string;
		manufacturingDate: string;
		pixhawk: string;
		company: string;
	}

	let fleetData: FleetItem[] = [];
	let loading = true;
	let error = '';
	let searchTerm = '';
	let statusFilter = '';
	let filteredData: FleetItem[] = [];
	let sortDirection: 'asc' | 'desc' = 'desc';
	let availableStatuses: GliderStatus[] = [];

	function transformGliderToFleetItem(glider: Glider): FleetItem {
		let status = glider.gliderStatus?.name || glider.gliderStatus?.toString() || 'Unknown';
		let manufacturingDate = glider.manufacturingDate
			? new Date(glider.manufacturingDate).toLocaleDateString('en-GB')
			: 'Unknown';

		return {
			name: glider.name || `${glider.line}-${glider.number}` || 'Unknown',
			ip: glider.vpnIp || 'Unknown',
			status,
			gliderStatus: (glider.gliderStatus as GliderStatus) || null,
			region:
				glider.region?.country ||
				glider.region?.name ||
				(typeof glider.region === 'string' ? glider.region : ''),
			manufacturingDate,
			pixhawk: glider.pixhawkUuid || '',
			company: glider.company?.name || (typeof glider.company === 'string' ? glider.company : '')
		};
	}

	async function loadFleetData() {
		try {
			loading = true;
			error = '';

			const gliders = await fetchGliders();
			fleetData = gliders.map(transformGliderToFleetItem);
			try {
				availableStatuses = await getAvailableStatuses();
			} catch (statusError) {
				console.warn('Could not load available statuses:', statusError);
				availableStatuses = [];
			}

			filterData();
		} catch (err) {
			console.error('Error loading fleet data:', err);
			error = 'Failed to load fleet data';
			fleetData = [];
		} finally {
			loading = false;
		}
	}
	$: statusOptions = [
		{ value: '', name: 'All Statuses' },
		...availableStatuses.map((status) => ({ value: status.name, name: status.name }))
	];

	function parseDroneName(name: string) {
		const match = name.match(/^M(\d+)-(\d+)$/i);
		const isHIL = name.toLowerCase().includes('hil');
		return match
			? {
					prefix: 'M',
					xx: parseInt(match[1], 10),
					yy: parseInt(match[2], 10),
					originalName: name,
					isHIL
				}
			: {
					prefix: 'Z',
					xx: 0,
					yy: 0,
					originalName: name,
					isHIL
				};
	}

	function sortFleetData(data: FleetItem[]): FleetItem[] {
		return [...data].sort((a, b) => {
			const A = parseDroneName(a.name),
				B = parseDroneName(b.name);
			if (A.isHIL !== B.isHIL) return A.isHIL ? 1 : -1;
			if (A.isHIL && B.isHIL) return A.originalName.localeCompare(B.originalName);
			let result =
				A.prefix !== B.prefix
					? A.prefix.localeCompare(B.prefix)
					: A.xx !== B.xx
						? A.xx - B.xx
						: A.yy - B.yy;
			return sortDirection === 'desc' ? -result : result;
		});
	}

	function toggleSort() {
		sortDirection = sortDirection === 'desc' ? 'asc' : 'desc';
		filterData();
	}

	function filterData() {
		const q = searchTerm.trim().toLowerCase();
		filteredData = sortFleetData(
			fleetData.filter((item) => {
				const matchesQuery =
					q === '' ||
					item.name.toLowerCase().includes(q) ||
					item.ip.toLowerCase().includes(q) ||
					item.region.toLowerCase().includes(q) ||
					item.pixhawk.toLowerCase().includes(q) ||
					item.status.toLowerCase().includes(q) ||
					item.company.toLowerCase().includes(q);
				const matchesStatus = statusFilter === '' || item.status === statusFilter;
				return matchesQuery && matchesStatus;
			})
		);
	}

	function clearFilters() {
		searchTerm = '';
		statusFilter = '';
		filterData();
	}

	$: filterData();
	onMount(loadFleetData);
</script>

<div class="p-6">
	<h1 class="mb-6 text-2xl font-bold text-gray-900">Fleets</h1>

	<!-- Filters and Actions -->
	<div class="mb-6 flex flex-wrap items-center gap-4">
		<!-- Status Filter -->
		<div class="flex items-center gap-2">
			<Select bind:value={statusFilter} class="w-48" on:change={filterData}>
				{#each statusOptions as option}
					<option value={option.value}>{option.name}</option>
				{/each}
			</Select>
		</div>

		<!-- Search -->
		<div class="flex max-w-md flex-1 items-center gap-2">
			<div class="relative flex-1">
				<SearchOutline
					class="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400"
				/>
				<Input bind:value={searchTerm} placeholder="Search" class="pl-10" on:input={filterData} />
			</div>
		</div>

		<!-- Action Buttons -->
		<div class="flex gap-2">
			<Button color="light" on:click={clearFilters}>Clear</Button>
			<Button color="blue" on:click={loadFleetData} disabled={loading}>
				{loading ? 'Loading...' : 'Refresh'}
			</Button>
			<Button
				color="green"
				href={environment.urlNewbornGliderWard}
				target="_blank"
				rel="noopener noreferrer">Register a new glider</Button
			>
		</div>
	</div>

	<!-- Loading State -->
	{#if loading}
		<div class="flex items-center justify-center py-12">
			<div class="h-8 w-8 animate-spin rounded-full border-b-2 border-green-600"></div>
			<span class="ml-3 text-gray-600">Loading fleet data...</span>
		</div>
	{:else if error}
		<!-- Error State -->
		<div class="mb-6 rounded-lg border border-red-200 bg-red-50 p-4">
			<div class="flex">
				<div class="text-red-600">
					<svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
						<path
							fill-rule="evenodd"
							d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
							clip-rule="evenodd"
						></path>
					</svg>
				</div>
				<div class="ml-3">
					<h3 class="text-sm font-medium text-red-800">Error loading fleet data</h3>
					<p class="mt-1 text-sm text-red-700">{error}</p>
				</div>
			</div>
		</div>
	{:else}
		<!-- Fleet Table -->
		<div class="overflow-hidden rounded-lg bg-white shadow">
			<Table class="w-full">
				<TableHead class="bg-green-600">
					<TableHeadCell class="p-0 font-semibold text-white">
						<button
							on:click={toggleSort}
							class="flex w-full items-center gap-2 px-4 py-3 text-left font-semibold transition-colors hover:bg-green-700"
						>
							Drone
							<span class="text-lg font-bold">
								{#if sortDirection === 'desc'}
									<span class="text-yellow-300">↓</span>
								{:else}
									<span class="text-yellow-300">↑</span>
								{/if}
							</span>
						</button>
					</TableHeadCell>
					<TableHeadCell class="font-semibold text-white">IP ↕</TableHeadCell>
					<TableHeadCell class="font-semibold text-white">Status ↕</TableHeadCell>
					<TableHeadCell class="font-semibold text-white">Region ↕</TableHeadCell>
					<TableHeadCell class="font-semibold text-white">Manufacturing Date ↕</TableHeadCell>
					<TableHeadCell class="font-semibold text-white">Pixhawk</TableHeadCell>
				</TableHead>
				<TableBody>
					{#each filteredData as item}
						<TableBodyRow class="hover:bg-gray-50">
							<TableBodyCell class="font-medium text-green-600">
								<a
									href={`/drone?id=${item.name}`}
									class="cursor-pointer hover:text-green-800 hover:underline"
								>
									{item.name}
								</a>
							</TableBodyCell>
							<TableBodyCell>{item.ip}</TableBodyCell>
							<TableBodyCell>
								{#if item.gliderStatus?.name}
									{@const backgroundColor = getStatusColor(item.gliderStatus)}
									{@const textColor = getTextColor(backgroundColor)}
									<span
										class="inline-flex min-w-[100px] items-center justify-center rounded-full px-3 py-1 text-sm font-medium"
										style="background-color: {backgroundColor}; color: {textColor};"
									>
										{item.gliderStatus.name}
									</span>
								{:else}
									<span
										class="inline-flex min-w-[100px] items-center justify-center rounded-full bg-gray-500 px-3 py-1 text-sm font-medium text-white"
									>
										{item.status}
									</span>
								{/if}
							</TableBodyCell>
							<TableBodyCell>{item.region}</TableBodyCell>
							<TableBodyCell>{item.manufacturingDate}</TableBodyCell>
							<TableBodyCell class="max-w-xs truncate" title={item.pixhawk}
								>{item.pixhawk}</TableBodyCell
							>
						</TableBodyRow>
					{/each}
				</TableBody>
			</Table>
		</div>

		{#if filteredData.length === 0}
			<div class="py-8 text-center text-gray-500">No fleets found matching your criteria.</div>
		{/if}
	{/if}
</div>
