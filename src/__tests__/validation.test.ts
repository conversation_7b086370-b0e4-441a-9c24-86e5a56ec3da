import { describe, it, expect, vi } from 'vitest';
import {
	validateScheduleRow,
	validateDateTime,
	hasValidationErrors,
	hasFieldError,
	getRowsWithErrors,
	getMissingFieldsSummary
} from '$lib/utils/validation';
import {
	createMockValidationContext,
	mockValidRow,
	mockExcelDateRow,
	mockStringDateRow,
	mockTimeStringRow
} from './mocks/common';

vi.mock('$lib/services/users.service', () => ({
	findPilotByEmailOrName: (pilots, query) => {
		return pilots.find(
			(p) =>
				p.email === query ||
				`${p.firstName} ${p.lastName}` === query ||
				p.firstName === query ||
				p.lastName === query
		);
	}
}));

vi.mock('$lib/utils/excel', () => ({
	excelDateToJSDate: (excelDate) => {
		const baseDate = new Date('1900-01-01');
		return new Date(baseDate.getTime() + (excelDate - 2) * 24 * 60 * 60 * 1000);
	}
}));

describe('Validation Utils', () => {
	const context = createMockValidationContext();

	describe('validateScheduleRow', () => {
		it('should validate a valid row successfully', () => {
			const result = validateScheduleRow(mockValidRow, context);

			expect(result._validationErrors).toHaveLength(0);
			expect(result._missingFields).toHaveLength(0);
			expect(result.glider_id).toBe('1');
			expect(result.start_location_id).toBe('1');
			expect(result.end_location_id).toBe('2');
			expect(result.pilot_id).toBe('pilot-1');
		});

		it('should detect missing glider', () => {
			const invalidRow = { ...mockValidRow, glider_name: 'Invalid Glider' };
			const result = validateScheduleRow(invalidRow, context);

			expect(result._validationErrors).toContain('Missing or invalid glider');
			expect(result._missingFields).toContain('glider');
		});

		it('should detect missing start location', () => {
			const invalidRow = { ...mockValidRow, start_location_name: 'Invalid Location' };
			const result = validateScheduleRow(invalidRow, context);

			expect(result._validationErrors).toContain('Missing or invalid start location');
			expect(result._missingFields).toContain('start_location');
		});

		it('should detect missing end location', () => {
			const invalidRow = { ...mockValidRow, end_location_name: 'Invalid Location' };
			const result = validateScheduleRow(invalidRow, context);

			expect(result._validationErrors).toContain('Missing or invalid end location');
			expect(result._missingFields).toContain('end_location');
		});

		it('should detect missing pilot', () => {
			const invalidRow = { ...mockValidRow, pilot_email: '<EMAIL>' };
			const result = validateScheduleRow(invalidRow, context);

			expect(result._validationErrors).toContain(
				'Pilot not found: <EMAIL>. Please check if the email exists in the system.'
			);
			expect(result._missingFields).toContain('pilot');
		});

		it('should handle Excel date format', () => {
			const result = validateScheduleRow(mockExcelDateRow, context);

			expect(result._validationErrors).toHaveLength(0);
			expect(result.scheduled_start_time).toBeInstanceOf(Date);
			expect(result.scheduled_end_time).toBeInstanceOf(Date);
		});

		it('should handle string date format', () => {
			const result = validateScheduleRow(mockStringDateRow, context);

			expect(result._validationErrors).toHaveLength(0);
			expect(result.scheduled_start_time).toBeInstanceOf(Date);
			expect(result.scheduled_end_time).toBeInstanceOf(Date);
		});

		it('should handle time string format', () => {
			const result = validateScheduleRow(mockTimeStringRow, context);

			expect(result._validationErrors).toHaveLength(0);
			expect(result.scheduled_start_time).toBeInstanceOf(Date);
			expect(result.scheduled_end_time).toBeInstanceOf(Date);
		});
	});

	describe('validateDateTime', () => {
		it('should validate valid Date objects', () => {
			const row = {
				scheduled_start_time: new Date('2024-01-15T10:00:00'),
				scheduled_end_time: new Date('2024-01-15T12:00:00')
			};

			const result = validateDateTime(row);

			expect(result.isValid).toBe(true);
			expect(result.errors).toHaveLength(0);
			expect(result.scheduledStartTime).toBeInstanceOf(Date);
			expect(result.scheduledEndTime).toBeInstanceOf(Date);
		});

		it('should validate Excel number dates', () => {
			const row = {
				date: 45307, // Excel date for 2024-01-15
				scheduled_start_time: 0.4167, // 10:00 AM
				scheduled_end_time: 0.5 // 12:00 PM
			};

			const result = validateDateTime(row);

			expect(result.isValid).toBe(true);
			expect(result.scheduledStartTime).toBeInstanceOf(Date);
			expect(result.scheduledEndTime).toBeInstanceOf(Date);
		});

		it('should handle invalid date formats', () => {
			const row = {
				scheduled_start_time: 'invalid-date'
			};

			const result = validateDateTime(row);

			expect(result.isValid).toBe(false);
			expect(result.errors).toHaveLength(1);
			expect(result.missingFields).toContain('start_time');
		});

		it('should handle missing required fields', () => {
			const row = {};

			const result = validateDateTime(row);

			expect(result.isValid).toBe(false);
			expect(result.missingFields).toContain('start_time');
			expect(result.missingFields).toContain('date');
		});
	});

	describe('utility functions', () => {
		it('should detect validation errors', () => {
			const rowWithErrors = { _validationErrors: ['Error 1', 'Error 2'], _missingFields: [] };
			const rowWithoutErrors = { _validationErrors: [], _missingFields: [] };

			expect(hasValidationErrors(rowWithErrors)).toBe(true);
			expect(hasValidationErrors(rowWithoutErrors)).toBe(false);
		});

		it('should detect field errors', () => {
			const row = { _validationErrors: [], _missingFields: ['glider', 'pilot'] };

			expect(hasFieldError(row, 'glider')).toBe(true);
			expect(hasFieldError(row, 'pilot')).toBe(true);
			expect(hasFieldError(row, 'location')).toBe(false);
		});

		it('should filter rows with errors', () => {
			const rows = [
				{ _validationErrors: [], _missingFields: [] },
				{ _validationErrors: ['Error'], _missingFields: ['field'] },
				{ _validationErrors: [], _missingFields: [] }
			];

			const rowsWithErrors = getRowsWithErrors(rows);

			expect(rowsWithErrors).toHaveLength(1);
			expect(rowsWithErrors[0]._validationErrors).toContain('Error');
		});

		it('should get missing fields summary', () => {
			const rowsWithErrors = [
				{ _validationErrors: ['Error 1'], _missingFields: ['glider', 'pilot'] },
				{ _validationErrors: ['Error 2'], _missingFields: ['pilot', 'location'] }
			];

			const summary = getMissingFieldsSummary(rowsWithErrors);

			expect(summary).toContain('glider');
			expect(summary).toContain('pilot');
			expect(summary).toContain('location');
			expect(summary).toHaveLength(3); // unique fields only
		});
	});
});
