import type { Glider, Location, User } from '$lib/types';

export const mockGliders: Glider[] = [
	{
		id: 1,
		line: 'M',
		generation: '24',
		number: '01',
		name: 'M24-01',
		pixhawkUuid: 'px4-uuid-001',
		gliderMode: { id: 1, name: 'Normal' },
		gliderStatus: { id: 1, name: 'Active' },
		autopilotSoftwareVersion: { id: 1, version: '1.0.0' },
		desiredAutopilotSoftwareVersion: { id: 1, version: '1.0.0' },
		jetsonSoftwareVersion: { id: 1, version: '1.0.0' },
		desiredJetsonSoftwareVersion: { id: 1, version: '1.0.0' },
		ftsPixhawkSoftwareVersion: { id: 1, version: '1.0.0' },
		desiredFtsPixhawkSoftwareVersion: { id: 1, version: '1.0.0' },
		ftsRaspiSoftwareVersion: { id: 1, version: '1.0.0' },
		desiredFtsRaspiSoftwareVersion: { id: 1, version: '1.0.0' },
		company: { id: 1, name: 'Test Company' },
		region: { id: 1, name: 'Test Region' },
		vpnIp: '********',
		vpnNetworkId: 'vpn-001',
		manufacturingDate: '2024-01-01',
		registrationCode: 'REG-001',
		registrationComplete: true,
		inUse: false,
		designDeviation: '',
		designComplianceRecord: '',
		totalFlightTimeInSeconds: 3600,
		totalFlightTimeSinceLastMaintenanceInSeconds: 1800,
		createdAt: '2024-01-01T00:00:00Z',
		updatedAt: '2024-01-01T00:00:00Z',
		flightPreConditionRead: null
	},
	{
		id: 2,
		line: 'M',
		generation: '24',
		number: '02',
		name: 'M24-02',
		pixhawkUuid: 'px4-uuid-002',
		gliderMode: { id: 1, name: 'Normal' },
		gliderStatus: { id: 1, name: 'Active' },
		autopilotSoftwareVersion: { id: 1, version: '1.0.0' },
		desiredAutopilotSoftwareVersion: { id: 1, version: '1.0.0' },
		jetsonSoftwareVersion: { id: 1, version: '1.0.0' },
		desiredJetsonSoftwareVersion: { id: 1, version: '1.0.0' },
		ftsPixhawkSoftwareVersion: { id: 1, version: '1.0.0' },
		desiredFtsPixhawkSoftwareVersion: { id: 1, version: '1.0.0' },
		ftsRaspiSoftwareVersion: { id: 1, version: '1.0.0' },
		desiredFtsRaspiSoftwareVersion: { id: 1, version: '1.0.0' },
		company: { id: 1, name: 'Test Company' },
		region: { id: 1, name: 'Test Region' },
		vpnIp: '********',
		vpnNetworkId: 'vpn-002',
		manufacturingDate: '2024-01-02',
		registrationCode: 'REG-002',
		registrationComplete: true,
		inUse: false,
		designDeviation: '',
		designComplianceRecord: '',
		totalFlightTimeInSeconds: 7200,
		totalFlightTimeSinceLastMaintenanceInSeconds: 3600,
		createdAt: '2024-01-01T00:00:00Z',
		updatedAt: '2024-01-01T00:00:00Z',
		flightPreConditionRead: null
	}
];

export const mockLocations: Location[] = [
	{
		id: 1,
		name: 'Airport Alpha',
		pictureUrl: 'https://example.com/alpha.jpg',
		videoUrl: 'https://example.com/alpha.mp4',
		latitude: 50.0,
		longitude: 10.0,
		radius: 1000,
		locationCategory: {
			id: 1,
			name: 'Airport',
			description: 'Commercial airport',
			createdAt: '2024-01-01T00:00:00Z',
			updatedAt: '2024-01-01T00:00:00Z'
		},
		locationStatus: {
			id: 1,
			name: 'Active',
			description: 'Location is active',
			createdAt: '2024-01-01T00:00:00Z',
			updatedAt: '2024-01-01T00:00:00Z'
		},
		createdAt: '2024-01-01T00:00:00Z',
		updatedAt: '2024-01-01T00:00:00Z'
	},
	{
		id: 2,
		name: 'Airport Beta',
		pictureUrl: 'https://example.com/beta.jpg',
		videoUrl: 'https://example.com/beta.mp4',
		latitude: 51.0,
		longitude: 11.0,
		radius: 1500,
		locationCategory: {
			id: 1,
			name: 'Airport',
			description: 'Commercial airport',
			createdAt: '2024-01-01T00:00:00Z',
			updatedAt: '2024-01-01T00:00:00Z'
		},
		locationStatus: {
			id: 1,
			name: 'Active',
			description: 'Location is active',
			createdAt: '2024-01-01T00:00:00Z',
			updatedAt: '2024-01-01T00:00:00Z'
		},
		createdAt: '2024-01-01T00:00:00Z',
		updatedAt: '2024-01-01T00:00:00Z'
	}
];

export const mockPilots: User[] = [
	{
		id: 'pilot-1',
		email: '<EMAIL>',
		firstName: 'John',
		lastName: 'Doe',
		role: 'pilot',
		status: 'active',
		created_at: '2024-01-01T00:00:00Z',
		updated_at: '2024-01-01T00:00:00Z'
	},
	{
		id: 'pilot-2',
		email: '<EMAIL>',
		firstName: 'Jane',
		lastName: 'Smith',
		role: 'pilot',
		status: 'active',
		created_at: '2024-01-01T00:00:00Z',
		updated_at: '2024-01-01T00:00:00Z'
	}
];

export const mockRideStatuses = [
	{
		id: 'status-1',
		name: 'Scheduled',
		color: '#3B82F6',
		is_default: true,
		created_at: '2024-01-01T00:00:00Z',
		updated_at: '2024-01-01T00:00:00Z'
	},
	{
		id: 'status-2',
		name: 'Completed',
		color: '#10B981',
		is_default: false,
		created_at: '2024-01-01T00:00:00Z',
		updated_at: '2024-01-01T00:00:00Z'
	}
];

export function createMockValidationContext() {
	const glidersMapByName = new Map(mockGliders.map((g) => [g.name, g]));
	const glidersMapById = new Map(mockGliders.map((g) => [g.id, g]));
	const locationsMapByName = new Map(mockLocations.map((l) => [l.name, l]));
	const locationsMapById = new Map(mockLocations.map((l) => [l.id, l]));
	const pilotsMapById = new Map(mockPilots.map((p) => [p.id, p]));

	return {
		gliders: mockGliders,
		locations: mockLocations,
		pilots: mockPilots,
		glidersMapByName,
		glidersMapById,
		locationsMapByName,
		locationsMapById,
		pilotsMapById
	};
}

export const mockValidRow = {
	glider_name: 'M24-01',
	start_location_name: 'Airport Alpha',
	end_location_name: 'Airport Beta',
	pilot_email: '<EMAIL>',
	scheduled_start_time: new Date('2024-01-15T10:00:00'),
	scheduled_end_time: new Date('2024-01-15T12:00:00'),
	notes: 'Test flight'
};

export const mockInvalidRow = {
	glider_name: 'Invalid Glider',
	start_location_name: 'Invalid Location',
	end_location_name: 'Invalid Location',
	pilot_email: '<EMAIL>',
	scheduled_start_time: 'invalid-time',
	notes: 'Test flight with errors'
};

export const mockExcelDateRow = {
	glider_name: 'M24-01',
	start_location_name: 'Airport Alpha',
	end_location_name: 'Airport Beta',
	pilot_email: '<EMAIL>',
	date: 45307,
	scheduled_start_time: 0.4167,
	scheduled_end_time: 0.5,
	notes: 'Excel date test'
};

export const mockStringDateRow = {
	glider_name: 'M24-01',
	start_location_name: 'Airport Alpha',
	end_location_name: 'Airport Beta',
	pilot_email: '<EMAIL>',
	scheduled_start_time: '2024-01-15T10:00:00',
	scheduled_end_time: '2024-01-15T12:00:00',
	notes: 'String date test'
};

export const mockTimeStringRow = {
	glider_name: 'M24-01',
	start_location_name: 'Airport Alpha',
	end_location_name: 'Airport Beta',
	pilot_email: '<EMAIL>',
	date: new Date('2024-01-15'),
	scheduled_start_time: '10:00',
	scheduled_end_time: '12:00',
	notes: 'Time string test'
};

export const mockFindPilotByEmailOrName = (pilots: User[], query: string) => {
	return pilots.find(
		(p) =>
			p.email === query ||
			`${p.firstName} ${p.lastName}` === query ||
			p.firstName === query ||
			p.lastName === query
	);
};
export const mockCreateRideResponse = {
	id: 'ride-123',
	from_location: 'location-1',
	to_location: 'location-2',
	departure_time: '2024-01-15T10:00:00Z',
	arrival_time: '2024-01-15T12:00:00Z',
	ride_status_id: 'status-1',
	glider_id: 'glider-1',
	operator_id: 'pilot-1',
	has_package: false,
	package_description: 'Test flight',
	route_id: null,
	cancel_reason: null,
	created_at: '2024-01-15T08:00:00Z',
	updated_at: '2024-01-15T08:00:00Z'
};

export const mockApiError = {
	message: 'API Error',
	response: {
		status: 400,
		data: {
			message: 'Bad Request'
		}
	}
};

export const mockNetworkError = {
	message: 'Failed to fetch'
};
