import { describe, it, expect, vi } from 'vitest';
vi.mock('$lib/stores', () => ({
	keycloakClient: {
		subscribe: vi.fn(),
		get: vi.fn(() => ({ token: 'mock-token' }))
	}
}));

vi.mock('svelte-sonner', () => ({
	toast: {
		error: vi.fn(),
		success: vi.fn()
	}
}));

vi.mock('$lib/services/rides.service', () => ({
	createRide: vi.fn()
}));

vi.mock('$lib/services/users.service', () => ({
	fetchUsers: vi.fn(() => Promise.resolve([])),
	findPilotByEmailOrName: vi.fn()
}));

vi.mock('$lib/services/ride-status.service', () => ({
	fetchRideStatuses: vi.fn(() => Promise.resolve([]))
}));

describe('Upload Schedule Validation', () => {
	const mockGliders = [
		{ id: 1, name: 'Glider 1' },
		{ id: 2, name: 'Glider 2' }
	];

	const mockLocations = [
		{ id: 1, name: 'Location A' },
		{ id: 2, name: 'Location B' }
	];

	const mockPilots = [
		{ id: 'pilot-1', email: '<EMAIL>', firstName: 'John', lastName: 'Doe' },
		{ id: 'pilot-2', email: '<EMAIL>', firstName: 'Jane', lastName: 'Smith' }
	];
	function createValidationContext() {
		const locationsMapById = new Map(mockLocations.map((l) => [l.id, l]));
		const locationsMapByName = new Map(mockLocations.map((l) => [l.name, l]));
		const glidersMapById = new Map(mockGliders.map((g) => [g.id, g]));
		const glidersMapByName = new Map(mockGliders.map((g) => [g.name, g]));
		const pilotsMapById = new Map(mockPilots.map((p) => [p.id, p]));

		return {
			locationsMapById,
			locationsMapByName,
			glidersMapById,
			glidersMapByName,
			pilotsMapById,
			gliders: mockGliders,
			locations: mockLocations,
			pilots: mockPilots
		};
	}
	function validateRow(row: any, context: any) {
		const validatedRow = { ...row, _validationErrors: [], _missingFields: [] };

		const startLocation =
			context.locationsMapByName.get(row.start_location_name) ||
			context.locationsMapById.get(row.start_location_id);
		const endLocation =
			context.locationsMapByName.get(row.end_location_name) ||
			context.locationsMapById.get(row.end_location_id);
		const glider =
			context.glidersMapByName.get(row.glider_name) || context.glidersMapById.get(row.glider_id);

		let pilot = null;
		if (row.pilot_email) {
			pilot = context.pilots.find((p: any) => p.email === row.pilot_email);
		}
		if (!pilot && row.pilot_name) {
			pilot = context.pilots.find((p: any) =>
				`${p.firstName} ${p.lastName}`.toLowerCase().includes(row.pilot_name.toLowerCase())
			);
		}
		if (!pilot && row.pilot_id && !row.pilot_id.includes('@')) {
			pilot = context.pilotsMapById.get(row.pilot_id);
		}

		if (!glider) {
			validatedRow._validationErrors.push('Missing or invalid glider');
			validatedRow._missingFields.push('glider');
		} else {
			validatedRow.glider_id = glider.id;
		}

		if (!startLocation) {
			validatedRow._validationErrors.push('Missing or invalid start location');
			validatedRow._missingFields.push('start_location');
		} else {
			validatedRow.start_location_id = startLocation.id;
		}

		if (!endLocation) {
			validatedRow._validationErrors.push('Missing or invalid end location');
			validatedRow._missingFields.push('end_location');
		} else {
			validatedRow.end_location_id = endLocation.id;
		}

		if (pilot) {
			validatedRow.pilot_id = pilot.id;
		} else if (row.pilot_email || row.pilot_name || row.pilot_id) {
			const pilotInfo = row.pilot_email || row.pilot_name || row.pilot_id;
			validatedRow._validationErrors.push(
				`Pilot not found: ${pilotInfo}. Please check if the email exists in the system.`
			);
			validatedRow._missingFields.push('pilot');
		} else {
			validatedRow._validationErrors.push('Missing pilot information');
			validatedRow._missingFields.push('pilot');
		}

		if (!row.scheduled_start_time && !row.date) {
			validatedRow._validationErrors.push('Invalid date or time format');
			validatedRow._missingFields.push('start_time');
			validatedRow._missingFields.push('date');
		}

		if (!row.scheduled_end_time) {
			validatedRow._validationErrors.push('Missing end time');
			validatedRow._missingFields.push('end_time');
		}

		return validatedRow;
	}

	describe('Required Fields Validation', () => {
		it('should validate all required fields are present', () => {
			const context = createValidationContext();
			const validRow = {
				glider_id: 1,
				start_location_id: 1,
				end_location_id: 2,
				pilot_id: 'pilot-1',
				scheduled_start_time: new Date(),
				scheduled_end_time: new Date(),
				date: new Date()
			};

			const result = validateRow(validRow, context);
			expect(result._validationErrors).toHaveLength(0);
			expect(result._missingFields).toHaveLength(0);
		});

		it('should detect missing glider', () => {
			const context = createValidationContext();
			const invalidRow = {
				start_location_id: 1,
				end_location_id: 2,
				pilot_id: 'pilot-1',
				scheduled_start_time: new Date(),
				scheduled_end_time: new Date()
			};

			const result = validateRow(invalidRow, context);
			expect(result._validationErrors).toContain('Missing or invalid glider');
			expect(result._missingFields).toContain('glider');
		});

		it('should detect missing start location', () => {
			const context = createValidationContext();
			const invalidRow = {
				glider_id: 1,
				end_location_id: 2,
				pilot_id: 'pilot-1',
				scheduled_start_time: new Date(),
				scheduled_end_time: new Date()
			};

			const result = validateRow(invalidRow, context);
			expect(result._validationErrors).toContain('Missing or invalid start location');
			expect(result._missingFields).toContain('start_location');
		});

		it('should detect missing end location', () => {
			const context = createValidationContext();
			const invalidRow = {
				glider_id: 1,
				start_location_id: 1,
				pilot_id: 'pilot-1',
				scheduled_start_time: new Date(),
				scheduled_end_time: new Date()
			};

			const result = validateRow(invalidRow, context);
			expect(result._validationErrors).toContain('Missing or invalid end location');
			expect(result._missingFields).toContain('end_location');
		});

		it('should detect missing pilot', () => {
			const context = createValidationContext();
			const invalidRow = {
				glider_id: 1,
				start_location_id: 1,
				end_location_id: 2,
				scheduled_start_time: new Date(),
				scheduled_end_time: new Date()
			};

			const result = validateRow(invalidRow, context);
			expect(result._validationErrors).toContain('Missing pilot information');
			expect(result._missingFields).toContain('pilot');
		});

		it('should detect missing start time and date', () => {
			const context = createValidationContext();
			const invalidRow = {
				glider_id: 1,
				start_location_id: 1,
				end_location_id: 2,
				pilot_id: 'pilot-1',
				scheduled_end_time: new Date()
			};

			const result = validateRow(invalidRow, context);
			expect(result._validationErrors).toContain('Invalid date or time format');
			expect(result._missingFields).toContain('start_time');
			expect(result._missingFields).toContain('date');
		});

		it('should detect missing end time', () => {
			const context = createValidationContext();
			const invalidRow = {
				glider_id: 1,
				start_location_id: 1,
				end_location_id: 2,
				pilot_id: 'pilot-1',
				scheduled_start_time: new Date()
			};

			const result = validateRow(invalidRow, context);
			expect(result._validationErrors).toContain('Missing end time');
			expect(result._missingFields).toContain('end_time');
		});

		it('should detect multiple missing fields', () => {
			const context = createValidationContext();
			const invalidRow = {};

			const result = validateRow(invalidRow, context);
			expect(result._validationErrors.length).toBeGreaterThan(1);
			expect(result._missingFields).toContain('glider');
			expect(result._missingFields).toContain('start_location');
			expect(result._missingFields).toContain('end_location');
			expect(result._missingFields).toContain('pilot');
			expect(result._missingFields).toContain('start_time');
			expect(result._missingFields).toContain('end_time');
		});
	});

	describe('Field Resolution', () => {
		it('should resolve glider by name', () => {
			const context = createValidationContext();
			const row = {
				glider_name: 'Glider 1',
				start_location_id: 1,
				end_location_id: 2,
				pilot_id: 'pilot-1',
				scheduled_start_time: new Date(),
				scheduled_end_time: new Date()
			};

			const result = validateRow(row, context);
			expect(result.glider_id).toBe(1);
			expect(result._validationErrors).not.toContain('Missing or invalid glider');
		});

		it('should resolve location by name', () => {
			const context = createValidationContext();
			const row = {
				glider_id: 1,
				start_location_name: 'Location A',
				end_location_name: 'Location B',
				pilot_id: 'pilot-1',
				scheduled_start_time: new Date(),
				scheduled_end_time: new Date()
			};

			const result = validateRow(row, context);
			expect(result.start_location_id).toBe(1);
			expect(result.end_location_id).toBe(2);
			expect(result._validationErrors).not.toContain('Missing or invalid start location');
			expect(result._validationErrors).not.toContain('Missing or invalid end location');
		});

		it('should resolve pilot by email', () => {
			const context = createValidationContext();
			const row = {
				glider_id: 1,
				start_location_id: 1,
				end_location_id: 2,
				pilot_email: '<EMAIL>',
				scheduled_start_time: new Date(),
				scheduled_end_time: new Date()
			};

			const result = validateRow(row, context);
			expect(result.pilot_id).toBe('pilot-1');
			expect(result._validationErrors).not.toContain('Missing pilot information');
		});
	});
});
