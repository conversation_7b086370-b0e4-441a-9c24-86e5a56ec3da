import { describe, it, expect, beforeEach } from 'vitest';

describe('Upload Schedule Display Functionality', () => {
	let mockGliders: any[];
	let mockLocations: any[];
	let mockPilots: any[];
	let mockGlidersMapByName: Map<string, any>;
	let mockLocationsMapByName: Map<string, any>;

	beforeEach(() => {
		mockGliders = [
			{ id: 'glider-1', name: 'Glider A' },
			{ id: 'glider-2', name: 'Glider B' }
		];

		mockLocations = [
			{ id: 'loc-1', name: 'Location A' },
			{ id: 'loc-2', name: 'Location B' }
		];

		mockPilots = [
			{ id: 'pilot-1', email: '<EMAIL>', firstName: 'John', lastName: 'Doe' },
			{ id: 'pilot-2', email: '<EMAIL>', firstName: 'Jane', lastName: 'Smith' }
		];

		mockGlidersMapByName = new Map();
		mockGliders.forEach((g) => mockGlidersMapByName.set(g.name, g));

		mockLocationsMapByName = new Map();
		mockLocations.forEach((l) => mockLocationsMapByName.set(l.name, l));
	});

	describe('mapRowDataToIds function', () => {
		function mapRowDataToIds(row: any) {
			if (row.glider_name && !row.glider_id) {
				const glider = mockGlidersMapByName.get(row.glider_name);
				if (glider) {
					row.glider_id = glider.id;
				}
			}

			// Map location names to IDs
			if (row.start_location_name && !row.start_location_id) {
				const location = mockLocationsMapByName.get(row.start_location_name);
				if (location) {
					row.start_location_id = location.id;
				}
			}

			if (row.end_location_name && !row.end_location_id) {
				const location = mockLocationsMapByName.get(row.end_location_name);
				if (location) {
					row.end_location_id = location.id;
				}
			}

			// Map pilot email/name to ID
			if ((row.pilot_email || row.pilot_name) && !row.pilot_id) {
				let pilot = null;

				if (row.pilot_email) {
					pilot = mockPilots.find((p) => p.email === row.pilot_email);
				}

				if (!pilot && row.pilot_name) {
					pilot = mockPilots.find(
						(p) =>
							`${p.firstName} ${p.lastName}` === row.pilot_name ||
							p.firstName === row.pilot_name ||
							p.lastName === row.pilot_name
					);
				}

				if (pilot) {
					row.pilot_id = pilot.id;
				}
			}
		}

		it('should map glider name to ID', () => {
			const row = { glider_name: 'Glider A' };
			mapRowDataToIds(row);
			expect(row.glider_id).toBe('glider-1');
		});

		it('should map location names to IDs', () => {
			const row = {
				start_location_name: 'Location A',
				end_location_name: 'Location B'
			};
			mapRowDataToIds(row);
			expect(row.start_location_id).toBe('loc-1');
			expect(row.end_location_id).toBe('loc-2');
		});

		it('should map pilot email to ID', () => {
			const row = { pilot_email: '<EMAIL>' };
			mapRowDataToIds(row);
			expect(row.pilot_id).toBe('pilot-1');
		});

		it('should map pilot name to ID', () => {
			const row = { pilot_name: 'John Doe' };
			mapRowDataToIds(row);
			expect(row.pilot_id).toBe('pilot-1');
		});

		it('should map pilot first name to ID', () => {
			const row = { pilot_name: 'Jane' };
			mapRowDataToIds(row);
			expect(row.pilot_id).toBe('pilot-2');
		});

		it('should not override existing IDs', () => {
			const row = {
				glider_name: 'Glider A',
				glider_id: 'existing-id'
			};
			mapRowDataToIds(row);
			expect(row.glider_id).toBe('existing-id');
		});

		it('should handle missing data gracefully', () => {
			const row = {
				glider_name: 'Non-existent Glider',
				pilot_email: '<EMAIL>'
			};
			mapRowDataToIds(row);
			expect(row.glider_id).toBeUndefined();
			expect(row.pilot_id).toBeUndefined();
		});
	});

	describe('Selection functionality', () => {
		it('should handle select all functionality', () => {
			const rowSelections = { 0: false, 1: false, 2: false };
			const validRows = [{}, {}, {}];
			const selectAll = true;
			const newSelections = {};
			validRows.forEach((_, index) => {
				newSelections[index] = selectAll;
			});

			expect(newSelections[0]).toBe(true);
			expect(newSelections[1]).toBe(true);
			expect(newSelections[2]).toBe(true);
		});

		it('should update select all state correctly', () => {
			const rowSelections1 = { 0: true, 1: true, 2: true };
			const rowSelections2 = { 0: true, 1: false, 2: true };
			const validRows = [{}, {}, {}];

			const selectedCount1 = Object.values(rowSelections1).filter(Boolean).length;
			const selectedCount2 = Object.values(rowSelections2).filter(Boolean).length;

			const selectAll1 = selectedCount1 === validRows.length && validRows.length > 0;
			const selectAll2 = selectedCount2 === validRows.length && validRows.length > 0;

			expect(selectAll1).toBe(true);
			expect(selectAll2).toBe(false);
		});
	});
});
